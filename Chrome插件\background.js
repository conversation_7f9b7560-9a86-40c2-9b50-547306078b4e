// 后台服务脚本 - 处理插件的后台逻辑

// 插件安装时的初始化
chrome.runtime.onInstalled.addListener((details) => {
  if (details.reason === 'install') {
    // 首次安装，设置默认配置
    const 默认配置 = {
      自动运行: false,
      检查间隔: 30000, // 30秒
      发货规则: {
        最小金额: 0,
        最大金额: 99999,
        排除地址: [],
        排除商品: [],
        默认快递公司: '顺丰速运',
        默认备注: '感谢购买，祝您生活愉快！',
        快递选择规则: {
          '北京': '顺丰速运',
          '上海': '顺丰速运',
          '广东': '顺丰速运',
          '江苏': '圆通速递',
          '浙江': '圆通速递'
        },
        单号池: {
          '顺丰速运': [],
          '圆通速递': [],
          '中通快递': [],
          '申通快递': [],
          '韵达速递': []
        }
      },
      通知设置: {
        发货成功通知: true,
        发货失败通知: true,
        每日统计通知: true
      },
      统计数据: {
        今日发货数量: 0,
        总发货数量: 0,
        最后重置日期: new Date().toDateString()
      }
    };
    
    chrome.storage.sync.set(默认配置);
    console.log('小红书自动发货助手安装完成');
  }
});

// 监听来自content script的消息
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
  switch (message.type) {
    case 'notification':
      // 发送系统通知
      chrome.notifications.create({
        type: 'basic',
        iconUrl: 'icons/icon48.png',
        title: message.title,
        message: message.message
      });
      break;
      
    case 'updateStats':
      // 更新统计数据
      更新统计数据(message.data);
      break;
      
    case 'getSettings':
      // 获取设置
      chrome.storage.sync.get(null, (settings) => {
        sendResponse(settings);
      });
      return true; // 保持消息通道开放
      
    case 'saveSettings':
      // 保存设置
      chrome.storage.sync.set(message.settings, () => {
        sendResponse({ success: true });
      });
      return true;
      
    case 'getShippingLog':
      // 获取发货日志
      chrome.storage.local.get(['发货日志'], (result) => {
        sendResponse(result.发货日志 || []);
      });
      return true;
      
    case 'clearShippingLog':
      // 清空发货日志
      chrome.storage.local.set({ 发货日志: [] }, () => {
        sendResponse({ success: true });
      });
      return true;
  }
});

// 更新统计数据
async function 更新统计数据(数据) {
  const result = await chrome.storage.sync.get(['统计数据']);
  const 统计 = result.统计数据 || {
    今日发货数量: 0,
    总发货数量: 0,
    最后重置日期: new Date().toDateString()
  };
  
  const 今日 = new Date().toDateString();
  
  // 检查是否需要重置今日数据
  if (统计.最后重置日期 !== 今日) {
    统计.今日发货数量 = 0;
    统计.最后重置日期 = 今日;
  }
  
  // 更新数据
  if (数据.发货成功) {
    统计.今日发货数量++;
    统计.总发货数量++;
  }
  
  // 保存更新后的统计数据
  chrome.storage.sync.set({ 统计数据: 统计 });
}

// 定时任务 - 每日统计通知
chrome.alarms.create('dailyStats', { 
  when: Date.now() + (24 * 60 * 60 * 1000), // 24小时后
  periodInMinutes: 24 * 60 // 每24小时重复
});

chrome.alarms.onAlarm.addListener(async (alarm) => {
  if (alarm.name === 'dailyStats') {
    const result = await chrome.storage.sync.get(['统计数据', '通知设置']);
    const 统计 = result.统计数据;
    const 通知设置 = result.通知设置;
    
    if (通知设置?.每日统计通知 && 统计?.今日发货数量 > 0) {
      chrome.notifications.create({
        type: 'basic',
        iconUrl: 'icons/icon48.png',
        title: '每日发货统计',
        message: `今日已自动发货 ${统计.今日发货数量} 个订单，总计 ${统计.总发货数量} 个订单`
      });
    }
  }
});

// 监听标签页更新，检测是否进入小红书商家后台
chrome.tabs.onUpdated.addListener((tabId, changeInfo, tab) => {
  if (changeInfo.status === 'complete' && tab.url) {
    if (tab.url.includes('ark.xiaohongshu.com') || 
        tab.url.includes('creator.xiaohongshu.com')) {
      // 向content script发送消息，通知页面已加载
      chrome.tabs.sendMessage(tabId, {
        type: 'pageLoaded',
        url: tab.url
      }).catch(() => {
        // 忽略错误，可能content script还未加载
      });
    }
  }
});

// 插件图标点击事件
chrome.action.onClicked.addListener((tab) => {
  // 如果当前页面是小红书商家后台，直接切换自动发货状态
  if (tab.url && (tab.url.includes('ark.xiaohongshu.com') || 
                  tab.url.includes('creator.xiaohongshu.com'))) {
    chrome.tabs.sendMessage(tab.id, {
      type: 'toggleAutoShipping'
    });
  }
});

// 监听存储变化，同步到所有标签页
chrome.storage.onChanged.addListener((changes, namespace) => {
  if (namespace === 'sync' && changes.自动运行) {
    // 通知所有相关标签页状态变化
    chrome.tabs.query({
      url: ['https://ark.xiaohongshu.com/*', 'https://creator.xiaohongshu.com/*']
    }, (tabs) => {
      tabs.forEach(tab => {
        chrome.tabs.sendMessage(tab.id, {
          type: 'settingsChanged',
          changes: changes
        }).catch(() => {
          // 忽略错误
        });
      });
    });
  }
});

// 错误处理
chrome.runtime.onSuspend.addListener(() => {
  console.log('小红书自动发货助手后台脚本暂停');
});

// 导出函数供其他脚本使用
if (typeof module !== 'undefined' && module.exports) {
  module.exports = {
    更新统计数据
  };
}
