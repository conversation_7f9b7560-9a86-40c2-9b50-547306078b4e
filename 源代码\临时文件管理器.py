#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
临时文件管理器
负责自动清理临时文件，确保系统整洁
"""

import os
import shutil
import time
import logging
from pathlib import Path
from datetime import datetime, timedelta
import atexit
import signal
import sys

class 临时文件管理器:
    """临时文件自动管理类"""
    
    def __init__(self, 临时文件夹路径="临时文件"):
        """
        初始化临时文件管理器
        
        Args:
            临时文件夹路径 (str): 临时文件夹的路径
        """
        self.临时文件夹路径 = Path(临时文件夹路径)
        self.清理间隔 = 3600  # 1小时清理一次
        self.文件过期时间 = 3600  # 文件超过1小时就过期
        self.运行标志 = True
        
        # 设置日志
        self._设置日志()
        
        # 注册退出时的清理函数
        atexit.register(self.程序退出清理)
        signal.signal(signal.SIGINT, self._信号处理)
        signal.signal(signal.SIGTERM, self._信号处理)
        
        # 启动时清理
        self.启动时清理()
    
    def _设置日志(self):
        """设置日志配置"""
        日志文件夹 = Path("永久文件/日志文件")
        日志文件夹.mkdir(parents=True, exist_ok=True)
        
        日志文件名 = f"临时文件清理_{datetime.now().strftime('%Y-%m-%d')}.log"
        日志文件路径 = 日志文件夹 / 日志文件名
        
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(日志文件路径, encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
    
    def _信号处理(self, signum, frame):
        """处理系统信号"""
        self.logger.info(f"接收到信号 {signum}，准备清理并退出")
        self.运行标志 = False
        self.程序退出清理()
        sys.exit(0)
    
    def 启动时清理(self):
        """程序启动时清理上次遗留的临时文件"""
        try:
            if self.临时文件夹路径.exists():
                self.logger.info("开始启动时清理...")
                清理数量 = self._清理文件夹(self.临时文件夹路径)
                self.logger.info(f"启动时清理完成，清理了 {清理数量} 个文件")
            else:
                self.logger.info("临时文件夹不存在，创建新的临时文件夹")
                self._创建临时文件夹结构()
        except Exception as e:
            self.logger.error(f"启动时清理失败: {e}")
    
    def _创建临时文件夹结构(self):
        """创建临时文件夹结构"""
        子文件夹列表 = ["缓存文件", "临时下载", "处理中文件"]
        
        self.临时文件夹路径.mkdir(exist_ok=True)
        for 子文件夹 in 子文件夹列表:
            (self.临时文件夹路径 / 子文件夹).mkdir(exist_ok=True)
        
        self.logger.info("临时文件夹结构创建完成")
    
    def 定期清理(self):
        """定期清理过期的临时文件"""
        while self.运行标志:
            try:
                time.sleep(self.清理间隔)
                if not self.运行标志:
                    break
                
                self.logger.info("开始定期清理...")
                清理数量 = self._清理过期文件()
                self.logger.info(f"定期清理完成，清理了 {清理数量} 个过期文件")
                
            except Exception as e:
                self.logger.error(f"定期清理出错: {e}")
    
    def _清理过期文件(self):
        """清理过期的临时文件"""
        清理数量 = 0
        当前时间 = time.time()
        
        if not self.临时文件夹路径.exists():
            return 清理数量
        
        for 文件路径 in self.临时文件夹路径.rglob("*"):
            if 文件路径.is_file():
                try:
                    文件修改时间 = 文件路径.stat().st_mtime
                    if 当前时间 - 文件修改时间 > self.文件过期时间:
                        文件路径.unlink()
                        清理数量 += 1
                        self.logger.debug(f"删除过期文件: {文件路径}")
                except Exception as e:
                    self.logger.warning(f"删除文件失败 {文件路径}: {e}")
        
        return 清理数量
    
    def _清理文件夹(self, 文件夹路径):
        """清理指定文件夹中的所有文件"""
        清理数量 = 0
        
        if not 文件夹路径.exists():
            return 清理数量
        
        for 项目 in 文件夹路径.iterdir():
            try:
                if 项目.is_file():
                    项目.unlink()
                    清理数量 += 1
                elif 项目.is_dir():
                    # 递归清理子文件夹
                    子文件夹清理数量 = self._清理文件夹(项目)
                    清理数量 += 子文件夹清理数量
                    # 如果子文件夹为空，删除它
                    try:
                        项目.rmdir()
                    except OSError:
                        pass  # 文件夹不为空，保留
            except Exception as e:
                self.logger.warning(f"清理项目失败 {项目}: {e}")
        
        return 清理数量
    
    def 程序退出清理(self):
        """程序退出时的清理工作"""
        try:
            self.logger.info("开始程序退出清理...")
            清理数量 = self._清理文件夹(self.临时文件夹路径)
            self.logger.info(f"程序退出清理完成，清理了 {清理数量} 个文件")
        except Exception as e:
            self.logger.error(f"程序退出清理失败: {e}")
    
    def 创建临时文件(self, 文件名, 子文件夹=""):
        """
        创建临时文件路径
        
        Args:
            文件名 (str): 文件名
            子文件夹 (str): 子文件夹名称（可选）
        
        Returns:
            Path: 临时文件的完整路径
        """
        if 子文件夹:
            文件夹路径 = self.临时文件夹路径 / 子文件夹
        else:
            文件夹路径 = self.临时文件夹路径
        
        文件夹路径.mkdir(parents=True, exist_ok=True)
        return 文件夹路径 / 文件名
    
    def 获取临时文件夹大小(self):
        """获取临时文件夹的总大小（字节）"""
        总大小 = 0
        if self.临时文件夹路径.exists():
            for 文件路径 in self.临时文件夹路径.rglob("*"):
                if 文件路径.is_file():
                    try:
                        总大小 += 文件路径.stat().st_size
                    except Exception:
                        pass
        return 总大小
    
    def 手动清理(self):
        """手动触发清理"""
        self.logger.info("开始手动清理...")
        清理数量 = self._清理文件夹(self.临时文件夹路径)
        self._创建临时文件夹结构()
        self.logger.info(f"手动清理完成，清理了 {清理数量} 个文件")
        return 清理数量


# 全局临时文件管理器实例
临时文件管理器实例 = None

def 获取临时文件管理器():
    """获取全局临时文件管理器实例"""
    global 临时文件管理器实例
    if 临时文件管理器实例 is None:
        临时文件管理器实例 = 临时文件管理器()
    return 临时文件管理器实例


if __name__ == "__main__":
    # 测试代码
    管理器 = 临时文件管理器()
    
    # 创建一些测试文件
    测试文件1 = 管理器.创建临时文件("测试文件1.txt", "缓存文件")
    测试文件2 = 管理器.创建临时文件("测试文件2.jpg", "临时下载")
    
    with open(测试文件1, 'w', encoding='utf-8') as f:
        f.write("这是一个测试文件")
    
    with open(测试文件2, 'w', encoding='utf-8') as f:
        f.write("这是另一个测试文件")
    
    print(f"临时文件夹大小: {管理器.获取临时文件夹大小()} 字节")
    
    # 手动清理测试
    清理数量 = 管理器.手动清理()
    print(f"清理了 {清理数量} 个文件")
