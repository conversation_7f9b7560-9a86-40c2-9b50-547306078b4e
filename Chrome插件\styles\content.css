/* 内容脚本样式 - 注入到小红书页面的样式 */

/* 控制面板样式 */
#xhs-auto-shipping-panel {
  position: fixed !important;
  top: 20px !important;
  right: 20px !important;
  width: 200px !important;
  background: #fff !important;
  border: 1px solid #ddd !important;
  border-radius: 8px !important;
  box-shadow: 0 4px 12px rgba(0,0,0,0.1) !important;
  z-index: 10000 !important;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
  font-size: 12px !important;
}

#xhs-auto-shipping-panel .panel-header {
  display: flex !important;
  justify-content: space-between !important;
  align-items: center !important;
  padding: 8px 12px !important;
  background: #ff2442 !important;
  color: white !important;
  border-radius: 8px 8px 0 0 !important;
  cursor: move !important;
}

#xhs-auto-shipping-panel .panel-title {
  font-weight: 600 !important;
}

#xhs-auto-shipping-panel .panel-toggle {
  background: none !important;
  border: none !important;
  color: white !important;
  cursor: pointer !important;
  font-size: 16px !important;
  padding: 0 !important;
  width: 20px !important;
  height: 20px !important;
}

#xhs-auto-shipping-panel .panel-content {
  padding: 12px !important;
}

#xhs-auto-shipping-panel .status-row,
#xhs-auto-shipping-panel .stats-row {
  display: flex !important;
  justify-content: space-between !important;
  margin-bottom: 8px !important;
}

#xhs-auto-shipping-panel .status-row:last-child,
#xhs-auto-shipping-panel .stats-row:last-child {
  margin-bottom: 0 !important;
}

#xhs-auto-shipping-panel .status-value {
  font-weight: 600 !important;
  padding: 2px 8px !important;
  border-radius: 4px !important;
}

#xhs-auto-shipping-panel .status-value.running {
  color: #52c41a !important;
  background: #f6ffed !important;
}

#xhs-auto-shipping-panel .status-value.stopped {
  color: #ff4d4f !important;
  background: #fff2f0 !important;
}

#xhs-auto-shipping-panel .control-row {
  margin: 12px 0 !important;
}

#xhs-auto-shipping-panel .control-btn {
  width: 100% !important;
  padding: 8px !important;
  border: none !important;
  border-radius: 4px !important;
  cursor: pointer !important;
  font-weight: 600 !important;
  transition: all 0.2s !important;
}

#xhs-auto-shipping-panel .control-btn.start {
  background: #52c41a !important;
  color: white !important;
}

#xhs-auto-shipping-panel .control-btn.stop {
  background: #ff4d4f !important;
  color: white !important;
}

#xhs-auto-shipping-panel .control-btn:hover {
  opacity: 0.8 !important;
}

#xhs-auto-shipping-panel .log-row {
  display: flex !important;
  gap: 8px !important;
  margin-top: 12px !important;
}

#xhs-auto-shipping-panel .log-btn,
#xhs-auto-shipping-panel .settings-btn {
  flex: 1 !important;
  padding: 6px !important;
  border: 1px solid #ddd !important;
  border-radius: 4px !important;
  background: white !important;
  cursor: pointer !important;
  font-size: 11px !important;
}

#xhs-auto-shipping-panel .log-btn:hover,
#xhs-auto-shipping-panel .settings-btn:hover {
  background: #f5f5f5 !important;
}

/* 日志模态框样式 */
#shipping-log-modal .modal-overlay {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  width: 100% !important;
  height: 100% !important;
  background: rgba(0,0,0,0.5) !important;
  z-index: 10001 !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

#shipping-log-modal .modal-content {
  background: white !important;
  border-radius: 8px !important;
  width: 80% !important;
  max-width: 600px !important;
  max-height: 80% !important;
  overflow: hidden !important;
}

#shipping-log-modal .modal-header {
  display: flex !important;
  justify-content: space-between !important;
  align-items: center !important;
  padding: 16px !important;
  border-bottom: 1px solid #eee !important;
}

#shipping-log-modal .close-btn {
  background: none !important;
  border: none !important;
  font-size: 24px !important;
  cursor: pointer !important;
}

#shipping-log-modal .modal-body {
  padding: 16px !important;
  max-height: 400px !important;
  overflow-y: auto !important;
}

#shipping-log-modal .log-item {
  border-bottom: 1px solid #f0f0f0 !important;
  padding: 12px 0 !important;
}

#shipping-log-modal .log-item:last-child {
  border-bottom: none !important;
}

#shipping-log-modal .log-time {
  font-size: 12px !important;
  color: #666 !important;
  margin-bottom: 4px !important;
}

#shipping-log-modal .log-details div {
  font-size: 14px !important;
  margin-bottom: 2px !important;
}

/* 确保样式不被页面样式覆盖 */
#xhs-auto-shipping-panel * {
  box-sizing: border-box !important;
  margin: 0 !important;
  padding: 0 !important;
}

#xhs-auto-shipping-panel .panel-header,
#xhs-auto-shipping-panel .panel-content,
#xhs-auto-shipping-panel .status-row,
#xhs-auto-shipping-panel .control-row,
#xhs-auto-shipping-panel .log-row {
  margin: 0 !important;
  padding: initial !important;
}

#xhs-auto-shipping-panel .panel-header {
  padding: 8px 12px !important;
}

#xhs-auto-shipping-panel .panel-content {
  padding: 12px !important;
}

#xhs-auto-shipping-panel .status-row,
#xhs-auto-shipping-panel .stats-row {
  margin-bottom: 8px !important;
}

#xhs-auto-shipping-panel .control-row {
  margin: 12px 0 !important;
}

#xhs-auto-shipping-panel .log-row {
  margin-top: 12px !important;
}

/* 防止与页面样式冲突 */
#xhs-auto-shipping-panel {
  all: initial !important;
  position: fixed !important;
  top: 20px !important;
  right: 20px !important;
  width: 200px !important;
  background: #fff !important;
  border: 1px solid #ddd !important;
  border-radius: 8px !important;
  box-shadow: 0 4px 12px rgba(0,0,0,0.1) !important;
  z-index: 10000 !important;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
  font-size: 12px !important;
  color: #333 !important;
}

/* 响应式设计 */
@media (max-width: 768px) {
  #xhs-auto-shipping-panel {
    width: 180px !important;
    top: 10px !important;
    right: 10px !important;
  }
  
  #xhs-auto-shipping-panel .panel-header {
    padding: 6px 10px !important;
  }
  
  #xhs-auto-shipping-panel .panel-content {
    padding: 10px !important;
  }
  
  #xhs-auto-shipping-panel .panel-title {
    font-size: 11px !important;
  }
}

/* 动画效果 */
@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(100%);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

#xhs-auto-shipping-panel {
  animation: slideIn 0.3s ease !important;
}

/* 拖拽时的样式 */
#xhs-auto-shipping-panel.dragging {
  opacity: 0.8 !important;
  transform: rotate(2deg) !important;
}
