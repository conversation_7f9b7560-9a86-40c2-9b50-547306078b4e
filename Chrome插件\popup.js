// 弹窗脚本 - 处理插件弹窗的交互逻辑

class PopupManager {
  constructor() {
    this.settings = {};
    this.stats = {};
    this.isLoading = false;
    this.init();
  }

  async init() {
    // 加载设置和统计数据
    await this.loadData();
    
    // 更新界面
    this.updateUI();
    
    // 绑定事件
    this.bindEvents();
    
    // 检查当前页面
    this.checkCurrentPage();
    
    // 加载最近活动
    this.loadRecentActivity();
  }

  async loadData() {
    return new Promise((resolve) => {
      chrome.storage.sync.get(null, (settings) => {
        this.settings = settings;
        this.stats = settings.统计数据 || {
          今日发货数量: 0,
          总发货数量: 0
        };
        resolve();
      });
    });
  }

  updateUI() {
    // 更新运行状态
    const statusElement = document.getElementById('running-status');
    const toggleBtn = document.getElementById('toggle-auto-shipping');
    const toggleText = document.getElementById('toggle-text');
    
    if (this.settings.自动运行) {
      statusElement.textContent = '运行中';
      statusElement.className = 'status-value running';
      toggleBtn.className = 'control-btn danger';
      toggleText.textContent = '停止自动发货';
    } else {
      statusElement.textContent = '已停止';
      statusElement.className = 'status-value stopped';
      toggleBtn.className = 'control-btn primary';
      toggleText.textContent = '开启自动发货';
    }

    // 更新统计数据
    document.getElementById('today-shipped').textContent = this.stats.今日发货数量 || 0;
    document.getElementById('total-shipped').textContent = this.stats.总发货数量 || 0;
    
    // 检查待发货订单数量
    this.checkPendingOrders();
  }

  bindEvents() {
    // 切换自动发货
    document.getElementById('toggle-auto-shipping').addEventListener('click', () => {
      this.toggleAutoShipping();
    });

    // 立即检查订单
    document.getElementById('manual-check').addEventListener('click', () => {
      this.manualCheck();
    });

    // 查看日志
    document.getElementById('view-logs').addEventListener('click', () => {
      this.viewLogs();
    });

    // 打开设置
    document.getElementById('open-settings').addEventListener('click', () => {
      chrome.runtime.openOptionsPage();
      window.close();
    });

    // 帮助
    document.getElementById('help').addEventListener('click', () => {
      this.showHelp();
    });

    // 测试模式
    document.getElementById('test-mode').addEventListener('click', () => {
      this.toggleTestMode();
    });

    // 反馈问题
    document.getElementById('feedback').addEventListener('click', () => {
      this.openFeedback();
    });

    // 关于
    document.getElementById('about').addEventListener('click', () => {
      this.showAbout();
    });
  }

  async checkCurrentPage() {
    try {
      const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
      const pageTypeElement = document.getElementById('page-type');

      if (tab.url.includes('ark.xiaohongshu.com') || tab.url.includes('creator.xiaohongshu.com')) {
        if (tab.url.includes('/order')) {
          pageTypeElement.textContent = '订单管理页面';
          pageTypeElement.className = 'status-value success';

          // 添加虚拟商品支持提示
          this.showVirtualProductTip();
        } else {
          pageTypeElement.textContent = '小红书商家后台';
          pageTypeElement.className = 'status-value warning';

          // 添加导航提示
          this.showNavigationTip();
        }
      } else {
        pageTypeElement.textContent = '非小红书页面';
        pageTypeElement.className = 'status-value error';

        // 移除提示
        this.removeTips();
      }
    } catch (error) {
      console.error('检查当前页面失败:', error);
    }
  }

  showVirtualProductTip() {
    // 移除现有提示
    this.removeTips();

    const tipElement = document.createElement('div');
    tipElement.className = 'page-tip virtual-tip';
    tipElement.innerHTML = `
      <div style="display: flex; align-items: center; gap: 6px; margin-bottom: 4px;">
        <span style="color: #52c41a;">✅</span>
        <span style="font-weight: 500;">支持虚拟商品发货</span>
      </div>
      <div style="font-size: 11px; color: #666; line-height: 1.4;">
        • 自动识别"无物流发货"按钮<br>
        • 一键确认虚拟商品发货<br>
        • 智能验证发货状态
      </div>
    `;
    tipElement.style.cssText = `
      font-size: 12px;
      color: #333;
      margin-top: 8px;
      padding: 8px;
      background: linear-gradient(135deg, #f6ffed, #f0f9ff);
      border-radius: 6px;
      border: 1px solid #b7eb8f;
    `;

    const container = document.querySelector('.status-section');
    container.appendChild(tipElement);
  }

  showNavigationTip() {
    // 移除现有提示
    this.removeTips();

    const tipElement = document.createElement('div');
    tipElement.className = 'page-tip navigation-tip';
    tipElement.innerHTML = `
      <div style="color: #fa8c16; margin-bottom: 4px;">
        💡 请导航到订单管理页面
      </div>
      <div style="font-size: 11px; color: #666;">
        在左侧菜单中找到"订单管理"或"待发货订单"
      </div>
    `;
    tipElement.style.cssText = `
      font-size: 12px;
      margin-top: 8px;
      padding: 8px;
      background: #fff7e6;
      border-radius: 6px;
      border: 1px solid #ffd591;
    `;

    const container = document.querySelector('.status-section');
    container.appendChild(tipElement);
  }

  removeTips() {
    const existingTips = document.querySelectorAll('.page-tip');
    existingTips.forEach(tip => tip.remove());
  }

  async checkPendingOrders() {
    try {
      const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
      
      if (tab.url.includes('ark.xiaohongshu.com') || tab.url.includes('creator.xiaohongshu.com')) {
        // 向content script请求待发货订单数量
        chrome.tabs.sendMessage(tab.id, { type: 'getPendingOrdersCount' }, (response) => {
          if (response && response.count !== undefined) {
            document.getElementById('pending-orders').textContent = response.count;
          }
        });
      }
    } catch (error) {
      console.error('检查待发货订单失败:', error);
    }
  }

  async toggleAutoShipping() {
    this.showLoading('切换状态中...');
    
    try {
      const newStatus = !this.settings.自动运行;
      
      // 保存新状态
      await new Promise((resolve) => {
        chrome.storage.sync.set({ 自动运行: newStatus }, resolve);
      });
      
      // 更新本地状态
      this.settings.自动运行 = newStatus;
      
      // 通知content script
      const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
      if (tab.url.includes('ark.xiaohongshu.com') || tab.url.includes('creator.xiaohongshu.com')) {
        chrome.tabs.sendMessage(tab.id, { type: 'toggleAutoShipping' });
      }
      
      // 更新界面
      this.updateUI();
      
      // 添加活动记录
      this.addActivity(newStatus ? '开启自动发货' : '停止自动发货');
      
    } catch (error) {
      console.error('切换自动发货状态失败:', error);
      this.showError('操作失败，请重试');
    } finally {
      this.hideLoading();
    }
  }

  async manualCheck() {
    this.showLoading('检查订单中...');
    
    try {
      const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
      
      if (!tab.url.includes('ark.xiaohongshu.com') && !tab.url.includes('creator.xiaohongshu.com')) {
        this.showError('请在小红书商家后台页面使用此功能');
        return;
      }
      
      // 向content script发送立即检查消息
      chrome.tabs.sendMessage(tab.id, { type: 'manualCheck' }, (response) => {
        if (response && response.success) {
          this.addActivity(`手动检查完成，处理了 ${response.processedCount || 0} 个订单`);
          this.checkPendingOrders(); // 更新待发货数量
        } else {
          this.showError('检查失败，请确保在订单管理页面');
        }
      });
      
    } catch (error) {
      console.error('手动检查失败:', error);
      this.showError('检查失败，请重试');
    } finally {
      this.hideLoading();
    }
  }

  viewLogs() {
    // 打开日志页面
    chrome.tabs.create({
      url: chrome.runtime.getURL('logs.html')
    });
    window.close();
  }

  showHelp() {
    const helpContent = `
      <div class="help-content">
        <h3>使用说明</h3>
        <ol>
          <li>登录小红书商家后台</li>
          <li>进入订单管理页面</li>
          <li>点击"开启自动发货"</li>
          <li>插件会自动监控并处理待发货订单</li>
        </ol>
        
        <h3>注意事项</h3>
        <ul>
          <li>请先在设置中配置发货规则</li>
          <li>建议先测试少量订单</li>
          <li>保持网络连接稳定</li>
          <li>定期检查发货日志</li>
        </ul>
        
        <h3>快捷键</h3>
        <ul>
          <li>Ctrl+Shift+S: 快速开启/关闭自动发货</li>
          <li>Ctrl+Shift+C: 立即检查订单</li>
        </ul>
      </div>
    `;
    
    this.showModal('帮助', helpContent);
  }

  showAbout() {
    const aboutContent = `
      <div class="about-content">
        <h3>小红书自动发货助手</h3>
        <p>版本: 1.0.0</p>
        <p>一个帮助小红书商家自动处理订单发货的浏览器插件</p>
        
        <h4>主要功能</h4>
        <ul>
          <li>自动监控待发货订单</li>
          <li>智能匹配快递公司</li>
          <li>批量处理发货操作</li>
          <li>详细的发货日志</li>
          <li>灵活的发货规则配置</li>
        </ul>
        
        <h4>技术支持</h4>
        <p>如有问题请通过反馈功能联系我们</p>
        
        <p class="copyright">© 2025 小红书自动发货助手</p>
      </div>
    `;
    
    this.showModal('关于', aboutContent);
  }

  openFeedback() {
    // 打开反馈页面
    chrome.tabs.create({
      url: 'https://github.com/your-repo/issues'
    });
    window.close();
  }

  async loadRecentActivity() {
    try {
      chrome.storage.local.get(['发货日志'], (result) => {
        const logs = result.发货日志 || [];
        const activityList = document.getElementById('activity-list');
        
        if (logs.length === 0) {
          activityList.innerHTML = `
            <div class="activity-item">
              <div class="activity-time">暂无记录</div>
              <div class="activity-desc">还没有发货记录</div>
            </div>
          `;
          return;
        }
        
        // 显示最近3条记录
        const recentLogs = logs.slice(0, 3);
        activityList.innerHTML = recentLogs.map(log => `
          <div class="activity-item">
            <div class="activity-time">${this.formatTime(log.时间)}</div>
            <div class="activity-desc">发货: ${log.订单号} (${log.快递公司})</div>
          </div>
        `).join('');
      });
    } catch (error) {
      console.error('加载最近活动失败:', error);
    }
  }

  addActivity(description) {
    const activityList = document.getElementById('activity-list');
    const newActivity = document.createElement('div');
    newActivity.className = 'activity-item';
    newActivity.innerHTML = `
      <div class="activity-time">${this.formatTime(new Date().toISOString())}</div>
      <div class="activity-desc">${description}</div>
    `;
    
    activityList.insertBefore(newActivity, activityList.firstChild);
    
    // 只保留最近3条
    const items = activityList.querySelectorAll('.activity-item');
    if (items.length > 3) {
      items[items.length - 1].remove();
    }
  }

  formatTime(isoString) {
    const date = new Date(isoString);
    const now = new Date();
    const diff = now - date;
    
    if (diff < 60000) { // 1分钟内
      return '刚刚';
    } else if (diff < 3600000) { // 1小时内
      return `${Math.floor(diff / 60000)}分钟前`;
    } else if (diff < 86400000) { // 24小时内
      return `${Math.floor(diff / 3600000)}小时前`;
    } else {
      return date.toLocaleDateString();
    }
  }

  showModal(title, content) {
    const modal = document.createElement('div');
    modal.className = 'modal-overlay';
    modal.innerHTML = `
      <div class="modal-content">
        <div class="modal-header">
          <h3>${title}</h3>
          <button class="modal-close">&times;</button>
        </div>
        <div class="modal-body">
          ${content}
        </div>
      </div>
    `;
    
    document.body.appendChild(modal);
    
    // 绑定关闭事件
    modal.querySelector('.modal-close').addEventListener('click', () => {
      modal.remove();
    });
    
    modal.addEventListener('click', (e) => {
      if (e.target === modal) {
        modal.remove();
      }
    });
  }

  showLoading(text = '处理中...') {
    this.isLoading = true;
    const overlay = document.getElementById('loading-overlay');
    const loadingText = overlay.querySelector('.loading-text');
    loadingText.textContent = text;
    overlay.style.display = 'flex';
  }

  hideLoading() {
    this.isLoading = false;
    const overlay = document.getElementById('loading-overlay');
    overlay.style.display = 'none';
  }

  showError(message) {
    // 简单的错误提示
    const errorDiv = document.createElement('div');
    errorDiv.className = 'error-toast';
    errorDiv.textContent = message;
    document.body.appendChild(errorDiv);

    setTimeout(() => {
      errorDiv.remove();
    }, 3000);
  }

  async toggleTestMode() {
    try {
      const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });

      // 发送测试模式切换消息到内容脚本
      chrome.tabs.sendMessage(tab.id, { type: 'toggleTestMode' }, (response) => {
        if (chrome.runtime.lastError) {
          this.showError('请先打开小红书商家后台页面');
          return;
        }

        // 更新按钮状态
        this.updateTestModeButton();

        // 显示测试选项
        this.showTestOptions();
      });
    } catch (error) {
      console.error('切换测试模式失败:', error);
      this.showError('切换测试模式失败');
    }
  }

  updateTestModeButton() {
    const testBtn = document.getElementById('test-mode');
    const testText = document.getElementById('test-mode-text');

    if (testBtn.classList.contains('active')) {
      testBtn.classList.remove('active');
      testText.textContent = '测试模式';
      testBtn.style.background = '';
      testBtn.style.color = '';
    } else {
      testBtn.classList.add('active');
      testText.textContent = '退出测试';
      testBtn.style.background = '#ff4d4f';
      testBtn.style.color = 'white';
    }
  }

  showTestOptions() {
    // 移除现有的测试选项
    const existingOptions = document.querySelector('.test-options');
    if (existingOptions) {
      existingOptions.remove();
      return;
    }

    // 创建测试选项面板
    const testOptions = document.createElement('div');
    testOptions.className = 'test-options';
    testOptions.innerHTML = `
      <div style="
        margin: 12px 0;
        padding: 12px;
        background: #f0f9ff;
        border: 1px solid #91d5ff;
        border-radius: 6px;
      ">
        <div style="font-weight: 600; margin-bottom: 8px; color: #1890ff;">
          🧪 测试功能
        </div>
        <button id="run-test" style="
          width: 100%;
          padding: 8px;
          background: #1890ff;
          color: white;
          border: none;
          border-radius: 4px;
          cursor: pointer;
          margin-bottom: 8px;
        ">运行完整测试</button>
        <div style="font-size: 11px; color: #666; line-height: 1.4;">
          • 检测页面结构<br>
          • 查找订单元素<br>
          • 识别发货按钮<br>
          • 验证插件功能
        </div>
      </div>
    `;

    // 插入到快捷操作后面
    const quickActions = document.querySelector('.quick-actions');
    quickActions.parentNode.insertBefore(testOptions, quickActions.nextSibling);

    // 绑定运行测试事件
    document.getElementById('run-test').addEventListener('click', () => {
      this.runTest();
    });
  }

  async runTest() {
    try {
      const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });

      this.showError('正在运行测试，请查看控制台...');

      // 发送运行测试消息到内容脚本
      chrome.tabs.sendMessage(tab.id, { type: 'runTest' }, (response) => {
        if (chrome.runtime.lastError) {
          this.showError('测试失败，请检查页面');
          return;
        }
      });
    } catch (error) {
      console.error('运行测试失败:', error);
      this.showError('运行测试失败');
    }
  }
}

// 初始化弹窗管理器
document.addEventListener('DOMContentLoaded', () => {
  new PopupManager();
});
