{"项目信息": {"项目名称": "小红书自动发货软件", "版本号": "1.0.0", "开发者": "开发团队", "创建日期": "2025-07-30", "描述": "自动化处理小红书订单发货的软件系统"}, "文件夹配置": {"永久文件夹": "永久文件", "临时文件夹": "临时文件", "源代码文件夹": "源代码", "文档文件夹": "文档", "测试文件夹": "测试文件"}, "临时文件管理": {"自动清理": true, "清理间隔秒数": 3600, "文件过期时间秒数": 3600, "启动时清理": true, "退出时清理": true, "允许的临时文件扩展名": [".tmp", ".cache", ".temp", ".processing", ".tmp.jpg", ".tmp.png", ".tmp.json"]}, "日志配置": {"日志级别": "INFO", "日志文件格式": "日志_YYYY-MM-DD.log", "最大日志文件大小MB": 10, "保留日志天数": 30, "控制台输出": true}, "数据库配置": {"数据库类型": "SQLite", "数据库文件名": "数据_主数据库.db", "备份间隔小时": 24, "最大备份数量": 7}, "网络配置": {"请求超时秒数": 30, "重试次数": 3, "重试间隔秒数": 5, "并发连接数": 10, "用户代理": "小红书自动发货软件/1.0.0"}, "安全配置": {"数据加密": true, "密钥文件": "密钥_主密钥.key", "会话超时分钟": 120, "最大登录尝试次数": 5}, "性能配置": {"内存限制MB": 512, "缓存大小MB": 100, "线程池大小": 5, "批处理大小": 50}, "小红书接口配置": {"基础URL": "https://www.xiaohongshu.com", "登录接口": "/api/sns/web/v1/login", "订单接口": "/api/sns/web/v1/orders", "发货接口": "/api/sns/web/v1/shipping", "请求头": {"Content-Type": "application/json", "Accept": "application/json"}}, "发货配置": {"默认快递公司": "顺丰速运", "支持的快递公司": ["顺丰速运", "圆通速递", "中通快递", "申通快递", "韵达速递", "百世快递", "德邦快递"], "自动发货": false, "发货确认": true}, "界面配置": {"主题": "默认", "语言": "中文", "窗口大小": {"宽度": 1200, "高度": 800}, "自动保存设置": true}, "开发配置": {"调试模式": false, "详细日志": false, "测试模式": false, "开发者工具": false}}