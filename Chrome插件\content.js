// 内容脚本 - 在小红书页面中运行的核心逻辑

class 小红书自动发货助手 {
  constructor() {
    this.运行状态 = false;
    this.监控定时器 = null;
    this.检查间隔 = 30000; // 30秒
    this.发货规则 = {};
    this.通知设置 = {};
    this.处理中订单 = new Set(); // 防止重复处理
    this.页面类型 = this.检测页面类型();
    
    this.初始化();
  }
  
  async 初始化() {
    console.log('小红书自动发货助手已加载');
    
    // 加载设置
    await this.加载设置();
    
    // 创建控制面板
    this.创建控制面板();
    
    // 监听消息
    this.监听消息();
    
    // 如果设置为自动运行，则开始监控
    if (this.运行状态) {
      this.开始监控();
    }
  }
  
  async 加载设置() {
    return new Promise((resolve) => {
      chrome.runtime.sendMessage({ type: 'getSettings' }, (settings) => {
        this.运行状态 = settings.自动运行 || false;
        this.检查间隔 = settings.检查间隔 || 30000;
        this.发货规则 = settings.发货规则 || {};
        this.通知设置 = settings.通知设置 || {};
        resolve();
      });
    });
  }
  
  检测页面类型() {
    const url = window.location.href;
    if (url.includes('/order')) {
      return '订单管理';
    } else if (url.includes('/dashboard')) {
      return '仪表板';
    } else {
      return '其他';
    }
  }
  
  创建控制面板() {
    // 创建浮动控制面板
    const 面板 = document.createElement('div');
    面板.id = 'xhs-auto-shipping-panel';
    面板.innerHTML = `
      <div class="panel-header">
        <span class="panel-title">🚚 自动发货</span>
        <button class="panel-toggle" id="panel-toggle">−</button>
      </div>
      <div class="panel-content" id="panel-content">
        <div class="status-row">
          <span class="status-label">状态:</span>
          <span class="status-value" id="status-value">${this.运行状态 ? '运行中' : '已停止'}</span>
        </div>
        <div class="control-row">
          <button class="control-btn ${this.运行状态 ? 'stop' : 'start'}" id="toggle-btn">
            ${this.运行状态 ? '停止监控' : '开始监控'}
          </button>
        </div>
        <div class="stats-row">
          <span class="stats-label">今日发货:</span>
          <span class="stats-value" id="today-count">0</span>
        </div>
        <div class="log-row">
          <button class="log-btn" id="view-log">查看日志</button>
          <button class="settings-btn" id="open-settings">设置</button>
        </div>
      </div>
    `;
    
    // 添加样式
    const 样式 = document.createElement('style');
    样式.textContent = `
      #xhs-auto-shipping-panel {
        position: fixed;
        top: 20px;
        right: 20px;
        width: 200px;
        background: #fff;
        border: 1px solid #ddd;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        z-index: 10000;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        font-size: 12px;
      }
      
      .panel-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 8px 12px;
        background: #ff2442;
        color: white;
        border-radius: 8px 8px 0 0;
        cursor: move;
      }
      
      .panel-title {
        font-weight: 600;
      }
      
      .panel-toggle {
        background: none;
        border: none;
        color: white;
        cursor: pointer;
        font-size: 16px;
        padding: 0;
        width: 20px;
        height: 20px;
      }
      
      .panel-content {
        padding: 12px;
      }
      
      .status-row, .stats-row {
        display: flex;
        justify-content: space-between;
        margin-bottom: 8px;
      }
      
      .status-value {
        color: ${this.运行状态 ? '#52c41a' : '#ff4d4f'};
        font-weight: 600;
      }
      
      .control-row {
        margin: 12px 0;
      }
      
      .control-btn {
        width: 100%;
        padding: 8px;
        border: none;
        border-radius: 4px;
        cursor: pointer;
        font-weight: 600;
        transition: all 0.2s;
      }
      
      .control-btn.start {
        background: #52c41a;
        color: white;
      }
      
      .control-btn.stop {
        background: #ff4d4f;
        color: white;
      }
      
      .control-btn:hover {
        opacity: 0.8;
      }
      
      .log-row {
        display: flex;
        gap: 8px;
        margin-top: 12px;
      }
      
      .log-btn, .settings-btn {
        flex: 1;
        padding: 6px;
        border: 1px solid #ddd;
        border-radius: 4px;
        background: white;
        cursor: pointer;
        font-size: 11px;
      }
      
      .log-btn:hover, .settings-btn:hover {
        background: #f5f5f5;
      }
    `;
    
    document.head.appendChild(样式);
    document.body.appendChild(面板);
    
    // 绑定事件
    this.绑定面板事件();
    
    // 使面板可拖拽
    this.使面板可拖拽(面板);
  }
  
  绑定面板事件() {
    // 切换面板显示/隐藏
    document.getElementById('panel-toggle').addEventListener('click', () => {
      const 内容 = document.getElementById('panel-content');
      const 按钮 = document.getElementById('panel-toggle');
      if (内容.style.display === 'none') {
        内容.style.display = 'block';
        按钮.textContent = '−';
      } else {
        内容.style.display = 'none';
        按钮.textContent = '+';
      }
    });
    
    // 开始/停止监控
    document.getElementById('toggle-btn').addEventListener('click', () => {
      this.切换监控状态();
    });
    
    // 查看日志
    document.getElementById('view-log').addEventListener('click', () => {
      this.显示发货日志();
    });
    
    // 打开设置
    document.getElementById('open-settings').addEventListener('click', () => {
      chrome.runtime.openOptionsPage();
    });
  }
  
  使面板可拖拽(面板) {
    let 拖拽中 = false;
    let 起始X = 0;
    let 起始Y = 0;
    let 起始左边距 = 0;
    let 起始顶边距 = 0;
    
    const 头部 = 面板.querySelector('.panel-header');
    
    头部.addEventListener('mousedown', (e) => {
      拖拽中 = true;
      起始X = e.clientX;
      起始Y = e.clientY;
      起始左边距 = parseInt(window.getComputedStyle(面板).left, 10);
      起始顶边距 = parseInt(window.getComputedStyle(面板).top, 10);
      document.addEventListener('mousemove', 拖拽移动);
      document.addEventListener('mouseup', 拖拽结束);
    });
    
    function 拖拽移动(e) {
      if (!拖拽中) return;
      const 新左边距 = 起始左边距 + e.clientX - 起始X;
      const 新顶边距 = 起始顶边距 + e.clientY - 起始Y;
      面板.style.left = 新左边距 + 'px';
      面板.style.top = 新顶边距 + 'px';
      面板.style.right = 'auto';
    }
    
    function 拖拽结束() {
      拖拽中 = false;
      document.removeEventListener('mousemove', 拖拽移动);
      document.removeEventListener('mouseup', 拖拽结束);
    }
  }
  
  监听消息() {
    chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
      switch (message.type) {
        case 'toggleAutoShipping':
          this.切换监控状态();
          break;
          
        case 'settingsChanged':
          this.处理设置变化(message.changes);
          break;
          
        case 'pageLoaded':
          this.页面类型 = this.检测页面类型();
          break;
      }
    });
  }
  
  async 切换监控状态() {
    this.运行状态 = !this.运行状态;
    
    // 保存状态到存储
    chrome.runtime.sendMessage({
      type: 'saveSettings',
      settings: { 自动运行: this.运行状态 }
    });
    
    // 更新界面
    this.更新控制面板状态();
    
    if (this.运行状态) {
      this.开始监控();
    } else {
      this.停止监控();
    }
  }
  
  更新控制面板状态() {
    const 状态值 = document.getElementById('status-value');
    const 切换按钮 = document.getElementById('toggle-btn');
    
    if (状态值) {
      状态值.textContent = this.运行状态 ? '运行中' : '已停止';
      状态值.style.color = this.运行状态 ? '#52c41a' : '#ff4d4f';
    }
    
    if (切换按钮) {
      切换按钮.textContent = this.运行状态 ? '停止监控' : '开始监控';
      切换按钮.className = `control-btn ${this.运行状态 ? 'stop' : 'start'}`;
    }
  }
  
  开始监控() {
    if (this.监控定时器) {
      clearInterval(this.监控定时器);
    }
    
    console.log('开始自动监控订单...');
    
    // 立即执行一次检查
    this.检查并处理订单();
    
    // 设置定时检查
    this.监控定时器 = setInterval(() => {
      this.检查并处理订单();
    }, this.检查间隔);
  }
  
  停止监控() {
    if (this.监控定时器) {
      clearInterval(this.监控定时器);
      this.监控定时器 = null;
    }
    console.log('停止自动监控');
  }
  
  async 检查并处理订单() {
    try {
      // 检查当前页面是否是订单管理页面
      if (this.页面类型 !== '订单管理' && !this.是订单页面()) {
        return;
      }
      
      // 获取待发货订单
      const 待发货订单 = this.获取待发货订单();
      
      if (待发货订单.length > 0) {
        console.log(`发现 ${待发货订单.length} 个待发货订单`);
        
        for (const 订单 of 待发货订单) {
          // 检查是否已在处理中
          if (this.处理中订单.has(订单.订单号)) {
            continue;
          }
          
          // 标记为处理中
          this.处理中订单.add(订单.订单号);
          
          try {
            await this.处理单个订单(订单);
          } finally {
            // 处理完成后移除标记
            this.处理中订单.delete(订单.订单号);
          }
          
          // 添加延迟避免操作过快
          await this.延迟(2000 + Math.random() * 1000);
        }
      }
      
    } catch (error) {
      console.error('订单处理出错:', error);
      this.发送通知('订单处理出错', error.message, 'error');
    }
  }
  
  是订单页面() {
    return window.location.href.includes('/order') || 
           document.querySelector('.order-list, .order-table, [class*="order"]') !== null;
  }
  
  获取待发货订单() {
    // 尝试多种选择器来适配不同的页面结构
    const 可能的选择器 = [
      '.order-item',
      '.order-row',
      'tr[class*="order"]',
      '[class*="order-list"] > div',
      '.ant-table-tbody tr'
    ];
    
    let 订单元素列表 = [];
    
    for (const 选择器 of 可能的选择器) {
      订单元素列表 = document.querySelectorAll(选择器);
      if (订单元素列表.length > 0) {
        break;
      }
    }
    
    const 待发货订单 = [];
    
    订单元素列表.forEach(元素 => {
      const 状态文本 = this.提取状态文本(元素);
      if (状态文本 && (状态文本.includes('待发货') || 状态文本.includes('待发送'))) {
        const 订单信息 = this.解析订单信息(元素);
        if (订单信息 && 订单信息.订单号) {
          待发货订单.push(订单信息);
        }
      }
    });
    
    return 待发货订单;
  }
  
  提取状态文本(元素) {
    const 状态选择器 = [
      '.order-status',
      '.status',
      '[class*="status"]',
      'td:last-child',
      '.ant-tag'
    ];
    
    for (const 选择器 of 状态选择器) {
      const 状态元素 = 元素.querySelector(选择器);
      if (状态元素) {
        return 状态元素.textContent.trim();
      }
    }
    
    return 元素.textContent;
  }
  
  解析订单信息(订单元素) {
    try {
      return {
        订单号: this.提取文本(订单元素, ['.order-number', '.order-id', '[class*="order-no"]']),
        买家昵称: this.提取文本(订单元素, ['.buyer-name', '.customer-name', '[class*="buyer"]']),
        商品名称: this.提取文本(订单元素, ['.product-name', '.goods-name', '[class*="product"]']),
        收货地址: this.提取文本(订单元素, ['.shipping-address', '.address', '[class*="address"]']),
        订单金额: this.提取文本(订单元素, ['.order-amount', '.price', '.amount', '[class*="price"]']),
        发货按钮: this.查找发货按钮(订单元素),
        订单元素: 订单元素
      };
    } catch (error) {
      console.error('解析订单信息失败:', error);
      return null;
    }
  }
  
  提取文本(父元素, 选择器列表) {
    for (const 选择器 of 选择器列表) {
      const 元素 = 父元素.querySelector(选择器);
      if (元素) {
        return 元素.textContent.trim();
      }
    }
    return '';
  }
  
  查找发货按钮(订单元素) {
    const 按钮选择器 = [
      'button[class*="ship"]',
      'button:contains("发货")',
      '.ship-button',
      '.shipping-btn',
      'a[href*="ship"]'
    ];
    
    for (const 选择器 of 按钮选择器) {
      const 按钮 = 订单元素.querySelector(选择器);
      if (按钮) {
        return 按钮;
      }
    }
    
    // 如果没找到特定按钮，查找包含"发货"文本的按钮
    const 所有按钮 = 订单元素.querySelectorAll('button, a');
    for (const 按钮 of 所有按钮) {
      if (按钮.textContent.includes('发货')) {
        return 按钮;
      }
    }
    
    return null;
  }
  
  // 延迟函数
  延迟(毫秒) {
    return new Promise(resolve => setTimeout(resolve, 毫秒));
  }
  
  // 发送通知
  发送通知(标题, 内容, 类型 = 'info') {
    chrome.runtime.sendMessage({
      type: 'notification',
      title: 标题,
      message: 内容
    });
  }
  
  // 显示发货日志
  显示发货日志() {
    chrome.runtime.sendMessage({ type: 'getShippingLog' }, (日志列表) => {
      // 创建日志显示窗口
      this.创建日志窗口(日志列表);
    });
  }
  
  创建日志窗口(日志列表) {
    // 移除已存在的日志窗口
    const 现有窗口 = document.getElementById('shipping-log-modal');
    if (现有窗口) {
      现有窗口.remove();
    }
    
    const 模态框 = document.createElement('div');
    模态框.id = 'shipping-log-modal';
    模态框.innerHTML = `
      <div class="modal-overlay">
        <div class="modal-content">
          <div class="modal-header">
            <h3>发货日志</h3>
            <button class="close-btn">&times;</button>
          </div>
          <div class="modal-body">
            <div class="log-list">
              ${日志列表.map(日志 => `
                <div class="log-item">
                  <div class="log-time">${new Date(日志.时间).toLocaleString()}</div>
                  <div class="log-details">
                    <div>订单号: ${日志.订单号}</div>
                    <div>快递: ${日志.快递公司} - ${日志.快递单号}</div>
                    <div>地址: ${日志.收货地址}</div>
                  </div>
                </div>
              `).join('')}
            </div>
          </div>
        </div>
      </div>
    `;
    
    // 添加样式
    const 样式 = document.createElement('style');
    样式.textContent = `
      #shipping-log-modal .modal-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0,0,0,0.5);
        z-index: 10001;
        display: flex;
        align-items: center;
        justify-content: center;
      }
      
      #shipping-log-modal .modal-content {
        background: white;
        border-radius: 8px;
        width: 80%;
        max-width: 600px;
        max-height: 80%;
        overflow: hidden;
      }
      
      #shipping-log-modal .modal-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 16px;
        border-bottom: 1px solid #eee;
      }
      
      #shipping-log-modal .close-btn {
        background: none;
        border: none;
        font-size: 24px;
        cursor: pointer;
      }
      
      #shipping-log-modal .modal-body {
        padding: 16px;
        max-height: 400px;
        overflow-y: auto;
      }
      
      #shipping-log-modal .log-item {
        border-bottom: 1px solid #f0f0f0;
        padding: 12px 0;
      }
      
      #shipping-log-modal .log-time {
        font-size: 12px;
        color: #666;
        margin-bottom: 4px;
      }
      
      #shipping-log-modal .log-details div {
        font-size: 14px;
        margin-bottom: 2px;
      }
    `;
    
    document.head.appendChild(样式);
    document.body.appendChild(模态框);
    
    // 绑定关闭事件
    模态框.querySelector('.close-btn').addEventListener('click', () => {
      模态框.remove();
    });
    
    模态框.querySelector('.modal-overlay').addEventListener('click', (e) => {
      if (e.target === e.currentTarget) {
        模态框.remove();
      }
    });
  }
  
  async 处理单个订单(订单信息) {
    try {
      console.log(`开始处理订单: ${订单信息.订单号}`);

      // 根据规则判断是否自动发货
      if (!this.应该自动发货(订单信息)) {
        console.log(`订单 ${订单信息.订单号} 不符合自动发货规则，跳过`);
        return;
      }

      // 生成发货信息
      const 发货信息 = this.生成发货信息(订单信息);

      // 点击发货按钮
      if (!订单信息.发货按钮) {
        throw new Error('未找到发货按钮');
      }

      订单信息.发货按钮.click();

      // 等待发货弹窗出现
      await this.等待元素出现('.modal, .ant-modal, [class*="modal"]', 5000);

      // 填写发货信息
      await this.填写发货信息(发货信息);

      // 确认发货
      const 确认按钮 = this.查找确认按钮();
      if (确认按钮) {
        确认按钮.click();

        // 等待操作完成
        await this.等待元素消失('.modal, .ant-modal, [class*="modal"]', 10000);

        console.log(`订单 ${订单信息.订单号} 发货成功`);

        if (this.通知设置.发货成功通知) {
          this.发送通知('发货成功', `订单 ${订单信息.订单号} 已自动发货`);
        }

        // 记录发货日志
        this.记录发货日志(订单信息, 发货信息);

        // 更新统计
        chrome.runtime.sendMessage({
          type: 'updateStats',
          data: { 发货成功: true }
        });

        // 更新今日发货数量显示
        this.更新今日发货数量();

      } else {
        throw new Error('未找到确认发货按钮');
      }

    } catch (error) {
      console.error(`订单 ${订单信息.订单号} 发货失败:`, error);

      if (this.通知设置.发货失败通知) {
        this.发送通知('发货失败', `订单 ${订单信息.订单号}: ${error.message}`);
      }

      // 如果有打开的弹窗，尝试关闭
      this.关闭弹窗();
    }
  }

  应该自动发货(订单信息) {
    const 规则 = this.发货规则;

    // 金额限制检查
    if (规则.最小金额 || 规则.最大金额) {
      const 金额文本 = 订单信息.订单金额.replace(/[^\d.]/g, '');
      const 金额 = parseFloat(金额文本);

      if (规则.最小金额 && 金额 < 规则.最小金额) {
        console.log(`订单金额 ${金额} 低于最小金额 ${规则.最小金额}`);
        return false;
      }

      if (规则.最大金额 && 金额 > 规则.最大金额) {
        console.log(`订单金额 ${金额} 超过最大金额 ${规则.最大金额}`);
        return false;
      }
    }

    // 地址排除检查
    if (规则.排除地址 && 规则.排除地址.length > 0) {
      for (const 排除地址 of 规则.排除地址) {
        if (订单信息.收货地址.includes(排除地址)) {
          console.log(`订单地址包含排除地址: ${排除地址}`);
          return false;
        }
      }
    }

    // 商品排除检查
    if (规则.排除商品 && 规则.排除商品.length > 0) {
      for (const 排除商品 of 规则.排除商品) {
        if (订单信息.商品名称.includes(排除商品)) {
          console.log(`订单商品包含排除商品: ${排除商品}`);
          return false;
        }
      }
    }

    return true;
  }

  生成发货信息(订单信息) {
    // 智能选择快递公司
    const 快递公司 = this.智能选择快递公司(订单信息.收货地址);

    // 获取快递单号
    const 快递单号 = this.获取快递单号(快递公司);

    return {
      快递公司,
      快递单号,
      发货备注: this.发货规则.默认备注 || ''
    };
  }

  智能选择快递公司(收货地址) {
    const 规则 = this.发货规则.快递选择规则 || {};

    // 根据地址关键词选择快递公司
    for (const [地区关键词, 快递公司] of Object.entries(规则)) {
      if (收货地址.includes(地区关键词)) {
        return 快递公司;
      }
    }

    // 返回默认快递公司
    return this.发货规则.默认快递公司 || '顺丰速运';
  }

  获取快递单号(快递公司) {
    const 单号池 = this.发货规则.单号池 || {};
    const 公司单号池 = 单号池[快递公司] || [];

    if (公司单号池.length > 0) {
      // 使用预设单号池中的单号
      const 单号 = 公司单号池.shift();

      // 更新单号池
      chrome.runtime.sendMessage({
        type: 'saveSettings',
        settings: {
          发货规则: {
            ...this.发货规则,
            单号池: {
              ...单号池,
              [快递公司]: 公司单号池
            }
          }
        }
      });

      return 单号;
    } else {
      // 生成临时单号
      return `AUTO${Date.now().toString().slice(-8)}`;
    }
  }

  async 填写发货信息(发货信息) {
    // 等待表单元素加载
    await this.延迟(1000);

    // 选择快递公司
    await this.选择快递公司(发货信息.快递公司);

    // 输入快递单号
    await this.输入快递单号(发货信息.快递单号);

    // 输入备注
    if (发货信息.发货备注) {
      await this.输入发货备注(发货信息.发货备注);
    }
  }

  async 选择快递公司(快递公司名称) {
    const 选择器列表 = [
      'select[name*="express"]',
      'select[name*="courier"]',
      '.ant-select',
      '[class*="express-select"]'
    ];

    for (const 选择器 of 选择器列表) {
      const 元素 = document.querySelector(选择器);
      if (元素) {
        if (元素.tagName === 'SELECT') {
          // 普通下拉框
          const 选项 = Array.from(元素.options).find(option =>
            option.text.includes(快递公司名称)
          );
          if (选项) {
            元素.value = 选项.value;
            元素.dispatchEvent(new Event('change', { bubbles: true }));
            return;
          }
        } else {
          // Ant Design 下拉框
          元素.click();
          await this.延迟(500);

          const 选项 = document.querySelector(`[title="${快递公司名称}"], [label="${快递公司名称}"]`);
          if (选项) {
            选项.click();
            return;
          }
        }
      }
    }

    console.warn(`未找到快递公司选择器或选项: ${快递公司名称}`);
  }

  async 输入快递单号(快递单号) {
    const 选择器列表 = [
      'input[name*="tracking"]',
      'input[name*="number"]',
      'input[placeholder*="单号"]',
      'input[placeholder*="快递"]'
    ];

    for (const 选择器 of 选择器列表) {
      const 输入框 = document.querySelector(选择器);
      if (输入框) {
        输入框.focus();
        输入框.value = '';

        // 模拟逐字输入
        for (const 字符 of 快递单号) {
          输入框.value += 字符;
          输入框.dispatchEvent(new Event('input', { bubbles: true }));
          await this.延迟(50 + Math.random() * 50);
        }

        输入框.dispatchEvent(new Event('change', { bubbles: true }));
        return;
      }
    }

    console.warn('未找到快递单号输入框');
  }

  async 输入发货备注(备注内容) {
    const 选择器列表 = [
      'textarea[name*="note"]',
      'textarea[name*="remark"]',
      'textarea[placeholder*="备注"]',
      'input[name*="note"]'
    ];

    for (const 选择器 of 选择器列表) {
      const 输入框 = document.querySelector(选择器);
      if (输入框) {
        输入框.focus();
        输入框.value = 备注内容;
        输入框.dispatchEvent(new Event('input', { bubbles: true }));
        输入框.dispatchEvent(new Event('change', { bubbles: true }));
        return;
      }
    }
  }

  查找确认按钮() {
    const 选择器列表 = [
      'button[class*="confirm"]',
      'button[class*="submit"]',
      'button:contains("确认")',
      'button:contains("发货")',
      '.ant-btn-primary'
    ];

    for (const 选择器 of 选择器列表) {
      const 按钮 = document.querySelector(选择器);
      if (按钮 && !按钮.disabled) {
        return 按钮;
      }
    }

    // 查找包含确认文本的按钮
    const 所有按钮 = document.querySelectorAll('button');
    for (const 按钮 of 所有按钮) {
      const 文本 = 按钮.textContent.trim();
      if ((文本.includes('确认') || 文本.includes('发货') || 文本.includes('提交')) && !按钮.disabled) {
        return 按钮;
      }
    }

    return null;
  }

  关闭弹窗() {
    const 关闭按钮选择器 = [
      '.ant-modal-close',
      '.modal-close',
      '[class*="close"]',
      'button:contains("取消")',
      'button:contains("关闭")'
    ];

    for (const 选择器 of 关闭按钮选择器) {
      const 按钮 = document.querySelector(选择器);
      if (按钮) {
        按钮.click();
        break;
      }
    }
  }

  async 等待元素出现(选择器, 超时 = 5000) {
    return new Promise((resolve, reject) => {
      const 开始时间 = Date.now();
      const 检查 = () => {
        const 元素 = document.querySelector(选择器);
        if (元素 && 元素.offsetParent !== null) {
          resolve(元素);
        } else if (Date.now() - 开始时间 > 超时) {
          reject(new Error(`等待元素超时: ${选择器}`));
        } else {
          setTimeout(检查, 100);
        }
      };
      检查();
    });
  }

  async 等待元素消失(选择器, 超时 = 5000) {
    return new Promise((resolve, reject) => {
      const 开始时间 = Date.now();
      const 检查 = () => {
        const 元素 = document.querySelector(选择器);
        if (!元素 || 元素.offsetParent === null) {
          resolve();
        } else if (Date.now() - 开始时间 > 超时) {
          reject(new Error(`等待元素消失超时: ${选择器}`));
        } else {
          setTimeout(检查, 100);
        }
      };
      检查();
    });
  }

  记录发货日志(订单信息, 发货信息) {
    const 日志 = {
      时间: new Date().toISOString(),
      订单号: 订单信息.订单号,
      买家昵称: 订单信息.买家昵称,
      商品名称: 订单信息.商品名称,
      快递公司: 发货信息.快递公司,
      快递单号: 发货信息.快递单号,
      收货地址: 订单信息.收货地址,
      订单金额: 订单信息.订单金额
    };

    chrome.storage.local.get(['发货日志'], (result) => {
      const 日志列表 = result.发货日志 || [];
      日志列表.unshift(日志); // 添加到开头

      // 只保留最近500条日志
      if (日志列表.length > 500) {
        日志列表.splice(500);
      }

      chrome.storage.local.set({ 发货日志: 日志列表 });
    });
  }

  更新今日发货数量() {
    chrome.storage.sync.get(['统计数据'], (result) => {
      const 统计 = result.统计数据 || { 今日发货数量: 0 };
      const 数量元素 = document.getElementById('today-count');
      if (数量元素) {
        数量元素.textContent = 统计.今日发货数量;
      }
    });
  }

  处理设置变化(changes) {
    if (changes.自动运行) {
      this.运行状态 = changes.自动运行.newValue;
      this.更新控制面板状态();

      if (this.运行状态) {
        this.开始监控();
      } else {
        this.停止监控();
      }
    }

    if (changes.发货规则) {
      this.发货规则 = changes.发货规则.newValue;
    }

    if (changes.通知设置) {
      this.通知设置 = changes.通知设置.newValue;
    }
  }
}

// 等待页面加载完成后初始化
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', () => {
    new 小红书自动发货助手();
  });
} else {
  new 小红书自动发货助手();
}
