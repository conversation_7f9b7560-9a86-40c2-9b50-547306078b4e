// 设置页面脚本
class OptionsManager {
  constructor() {
    this.settings = {};
    this.currentExpressTab = '顺丰速运';
    this.init();
  }

  async init() {
    // 加载设置
    await this.loadSettings();
    
    // 绑定事件
    this.bindEvents();
    
    // 更新界面
    this.updateUI();
  }

  async loadSettings() {
    return new Promise((resolve) => {
      chrome.storage.sync.get(null, (settings) => {
        this.settings = settings;
        resolve();
      });
    });
  }

  bindEvents() {
    // 标签切换
    document.querySelectorAll('.tab-btn').forEach(btn => {
      btn.addEventListener('click', (e) => {
        this.switchTab(e.target.dataset.tab);
      });
    });

    // 保存设置
    document.getElementById('save-settings').addEventListener('click', () => {
      this.saveSettings();
    });

    // 重置表单
    document.getElementById('reset-form').addEventListener('click', () => {
      this.resetForm();
    });

    // 添加快递规则
    document.getElementById('add-express-rule').addEventListener('click', () => {
      this.addExpressRule();
    });

    // 快递单号池标签切换
    document.querySelectorAll('.pool-tab').forEach(tab => {
      tab.addEventListener('click', (e) => {
        this.switchExpressTab(e.target.dataset.express);
      });
    });

    // 导入单号
    document.getElementById('import-tracking').addEventListener('click', () => {
      this.importTrackingNumbers();
    });

    // 数据管理
    document.getElementById('export-settings').addEventListener('click', () => {
      this.exportSettings();
    });

    document.getElementById('import-settings').addEventListener('click', () => {
      document.getElementById('import-file').click();
    });

    document.getElementById('import-file').addEventListener('change', (e) => {
      this.importSettings(e.target.files[0]);
    });

    document.getElementById('clear-logs').addEventListener('click', () => {
      this.clearLogs();
    });

    document.getElementById('reset-settings').addEventListener('click', () => {
      this.resetSettings();
    });
  }

  switchTab(tabName) {
    // 更新标签按钮状态
    document.querySelectorAll('.tab-btn').forEach(btn => {
      btn.classList.remove('active');
    });
    document.querySelector(`[data-tab="${tabName}"]`).classList.add('active');

    // 更新内容区域
    document.querySelectorAll('.tab-content').forEach(content => {
      content.classList.remove('active');
    });
    document.getElementById(tabName).classList.add('active');
  }

  switchExpressTab(expressName) {
    this.currentExpressTab = expressName;
    
    // 更新标签状态
    document.querySelectorAll('.pool-tab').forEach(tab => {
      tab.classList.remove('active');
    });
    document.querySelector(`[data-express="${expressName}"]`).classList.add('active');

    // 更新单号池内容
    this.updateTrackingPool();
  }

  updateUI() {
    const settings = this.settings;

    // 基础设置
    document.getElementById('auto-start').checked = settings.自动运行 || false;
    document.getElementById('check-interval').value = settings.检查间隔 || 30000;
    document.getElementById('max-concurrent').value = settings.最大并发 || 3;
    document.getElementById('operation-delay').value = settings.操作延迟 || 2000;

    // 发货规则
    const rules = settings.发货规则 || {};
    document.getElementById('min-amount').value = rules.最小金额 || 0;
    document.getElementById('max-amount').value = rules.最大金额 || 99999;
    document.getElementById('exclude-addresses').value = (rules.排除地址 || []).join('\n');
    document.getElementById('exclude-products').value = (rules.排除商品 || []).join('\n');
    document.getElementById('default-note').value = rules.默认备注 || '';

    // 快递设置
    document.getElementById('default-express').value = rules.默认快递公司 || '顺丰速运';
    this.updateExpressRules();
    this.updateTrackingPool();

    // 通知设置
    const notifications = settings.通知设置 || {};
    document.getElementById('notify-success').checked = notifications.发货成功通知 !== false;
    document.getElementById('notify-error').checked = notifications.发货失败通知 !== false;
    document.getElementById('notify-daily').checked = notifications.每日统计通知 !== false;
    document.getElementById('sound-notification').checked = notifications.声音提醒 || false;

    // 高级设置
    document.getElementById('debug-mode').checked = settings.调试模式 || false;
    document.getElementById('auto-retry').checked = settings.自动重试 !== false;
    document.getElementById('retry-count').value = settings.重试次数 || 3;
    document.getElementById('retry-delay').value = settings.重试延迟 || 10;
    document.getElementById('log-retention').value = settings.日志保留天数 || 30;
  }

  updateExpressRules() {
    const rules = this.settings.发货规则?.快递选择规则 || {};
    const container = document.getElementById('express-rules');
    
    container.innerHTML = '';
    
    Object.entries(rules).forEach(([region, express]) => {
      this.addExpressRuleItem(region, express);
    });

    if (Object.keys(rules).length === 0) {
      this.addExpressRuleItem('', '');
    }
  }

  addExpressRule() {
    this.addExpressRuleItem('', '');
  }

  addExpressRuleItem(region = '', express = '') {
    const container = document.getElementById('express-rules');
    const ruleItem = document.createElement('div');
    ruleItem.className = 'rule-item';
    ruleItem.innerHTML = `
      <input type="text" placeholder="地区关键词" class="region-input" value="${region}">
      <select class="express-select">
        <option value="顺丰速运" ${express === '顺丰速运' ? 'selected' : ''}>顺丰速运</option>
        <option value="圆通速递" ${express === '圆通速递' ? 'selected' : ''}>圆通速递</option>
        <option value="中通快递" ${express === '中通快递' ? 'selected' : ''}>中通快递</option>
        <option value="申通快递" ${express === '申通快递' ? 'selected' : ''}>申通快递</option>
        <option value="韵达速递" ${express === '韵达速递' ? 'selected' : ''}>韵达速递</option>
      </select>
      <button type="button" class="remove-rule">删除</button>
    `;

    // 绑定删除事件
    ruleItem.querySelector('.remove-rule').addEventListener('click', () => {
      ruleItem.remove();
    });

    container.appendChild(ruleItem);
  }

  updateTrackingPool() {
    const pool = this.settings.发货规则?.单号池 || {};
    const numbers = pool[this.currentExpressTab] || [];
    
    document.getElementById('tracking-numbers').value = numbers.join('\n');
    document.getElementById('remaining-count').textContent = numbers.length;
  }

  importTrackingNumbers() {
    const textarea = document.getElementById('tracking-numbers');
    const numbers = textarea.value.split('\n').filter(n => n.trim());
    
    // 更新设置中的单号池
    if (!this.settings.发货规则) this.settings.发货规则 = {};
    if (!this.settings.发货规则.单号池) this.settings.发货规则.单号池 = {};
    
    this.settings.发货规则.单号池[this.currentExpressTab] = numbers;
    
    // 更新显示
    document.getElementById('remaining-count').textContent = numbers.length;
    
    this.showStatus('单号导入成功', 'success');
  }

  async saveSettings() {
    try {
      // 收集所有设置
      const newSettings = {
        自动运行: document.getElementById('auto-start').checked,
        检查间隔: parseInt(document.getElementById('check-interval').value) * 1000,
        最大并发: parseInt(document.getElementById('max-concurrent').value),
        操作延迟: parseInt(document.getElementById('operation-delay').value),
        
        发货规则: {
          最小金额: parseFloat(document.getElementById('min-amount').value),
          最大金额: parseFloat(document.getElementById('max-amount').value),
          排除地址: document.getElementById('exclude-addresses').value.split('\n').filter(a => a.trim()),
          排除商品: document.getElementById('exclude-products').value.split('\n').filter(p => p.trim()),
          默认备注: document.getElementById('default-note').value,
          默认快递公司: document.getElementById('default-express').value,
          快递选择规则: this.getExpressRules(),
          单号池: this.settings.发货规则?.单号池 || {}
        },
        
        通知设置: {
          发货成功通知: document.getElementById('notify-success').checked,
          发货失败通知: document.getElementById('notify-error').checked,
          每日统计通知: document.getElementById('notify-daily').checked,
          声音提醒: document.getElementById('sound-notification').checked
        },
        
        调试模式: document.getElementById('debug-mode').checked,
        自动重试: document.getElementById('auto-retry').checked,
        重试次数: parseInt(document.getElementById('retry-count').value),
        重试延迟: parseInt(document.getElementById('retry-delay').value),
        日志保留天数: parseInt(document.getElementById('log-retention').value),
        
        统计数据: this.settings.统计数据 || {}
      };

      // 保存到存储
      await new Promise((resolve) => {
        chrome.storage.sync.set(newSettings, resolve);
      });

      this.settings = newSettings;
      this.showStatus('设置保存成功', 'success');

    } catch (error) {
      console.error('保存设置失败:', error);
      this.showStatus('保存失败，请重试', 'error');
    }
  }

  getExpressRules() {
    const rules = {};
    document.querySelectorAll('.rule-item').forEach(item => {
      const region = item.querySelector('.region-input').value.trim();
      const express = item.querySelector('.express-select').value;
      if (region) {
        rules[region] = express;
      }
    });
    return rules;
  }

  resetForm() {
    if (confirm('确定要重置表单吗？未保存的更改将丢失。')) {
      this.updateUI();
      this.showStatus('表单已重置', 'success');
    }
  }

  exportSettings() {
    const dataStr = JSON.stringify(this.settings, null, 2);
    const dataBlob = new Blob([dataStr], { type: 'application/json' });
    
    const link = document.createElement('a');
    link.href = URL.createObjectURL(dataBlob);
    link.download = `小红书自动发货设置_${new Date().toISOString().slice(0, 10)}.json`;
    link.click();
    
    this.showStatus('设置导出成功', 'success');
  }

  async importSettings(file) {
    if (!file) return;
    
    try {
      const text = await file.text();
      const settings = JSON.parse(text);
      
      await new Promise((resolve) => {
        chrome.storage.sync.set(settings, resolve);
      });
      
      this.settings = settings;
      this.updateUI();
      this.showStatus('设置导入成功', 'success');
      
    } catch (error) {
      console.error('导入设置失败:', error);
      this.showStatus('导入失败，请检查文件格式', 'error');
    }
  }

  async clearLogs() {
    if (confirm('确定要清空所有日志吗？此操作不可恢复。')) {
      await new Promise((resolve) => {
        chrome.storage.local.set({ 发货日志: [] }, resolve);
      });
      this.showStatus('日志已清空', 'success');
    }
  }

  async resetSettings() {
    if (confirm('确定要重置所有设置吗？此操作不可恢复。')) {
      await new Promise((resolve) => {
        chrome.storage.sync.clear(resolve);
      });
      
      // 重新加载默认设置
      await this.loadSettings();
      this.updateUI();
      this.showStatus('设置已重置', 'success');
    }
  }

  showStatus(message, type = 'success') {
    const statusElement = document.getElementById('save-status');
    statusElement.textContent = message;
    statusElement.className = `save-status ${type}`;
    
    setTimeout(() => {
      statusElement.textContent = '';
      statusElement.className = 'save-status';
    }, 3000);
  }
}

// 初始化设置管理器
document.addEventListener('DOMContentLoaded', () => {
  new OptionsManager();
});
