<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>小红书自动发货助手</title>
    <link rel="stylesheet" href="styles/popup.css">
</head>
<body>
    <div class="popup-container">
        <!-- 头部 -->
        <div class="header">
            <div class="logo">
                <span class="icon">🚚</span>
                <span class="title">自动发货助手</span>
            </div>
            <div class="version">v1.0.0</div>
        </div>

        <!-- 状态区域 -->
        <div class="status-section">
            <div class="status-item">
                <span class="status-label">运行状态:</span>
                <span class="status-value" id="running-status">检测中...</span>
            </div>
            <div class="status-item">
                <span class="status-label">当前页面:</span>
                <span class="status-value" id="page-type">未知</span>
            </div>
        </div>

        <!-- 控制区域 -->
        <div class="control-section">
            <button class="control-btn primary" id="toggle-auto-shipping">
                <span id="toggle-text">开启自动发货</span>
            </button>
            <button class="control-btn secondary" id="manual-check">
                立即检查订单
            </button>
        </div>

        <!-- 统计区域 -->
        <div class="stats-section">
            <div class="stats-title">发货统计</div>
            <div class="stats-grid">
                <div class="stats-item">
                    <div class="stats-number" id="today-shipped">0</div>
                    <div class="stats-label">今日发货</div>
                </div>
                <div class="stats-item">
                    <div class="stats-number" id="total-shipped">0</div>
                    <div class="stats-label">总计发货</div>
                </div>
                <div class="stats-item">
                    <div class="stats-number" id="pending-orders">0</div>
                    <div class="stats-label">待发货</div>
                </div>
            </div>
        </div>

        <!-- 快捷操作 -->
        <div class="quick-actions">
            <button class="action-btn" id="view-logs">
                <span class="action-icon">📋</span>
                <span class="action-text">查看日志</span>
            </button>
            <button class="action-btn" id="open-settings">
                <span class="action-icon">⚙️</span>
                <span class="action-text">设置</span>
            </button>
            <button class="action-btn test-btn" id="test-mode">
                <span class="action-icon">🧪</span>
                <span class="action-text" id="test-mode-text">测试模式</span>
            </button>
            <button class="action-btn" id="help">
                <span class="action-icon">❓</span>
                <span class="action-text">帮助</span>
            </button>
        </div>

        <!-- 最近活动 -->
        <div class="recent-activity">
            <div class="activity-title">最近活动</div>
            <div class="activity-list" id="activity-list">
                <div class="activity-item">
                    <div class="activity-time">等待数据...</div>
                    <div class="activity-desc">插件已启动</div>
                </div>
            </div>
        </div>

        <!-- 底部链接 -->
        <div class="footer">
            <a href="#" id="feedback">反馈问题</a>
            <span class="separator">|</span>
            <a href="#" id="about">关于</a>
        </div>
    </div>

    <!-- 加载中遮罩 -->
    <div class="loading-overlay" id="loading-overlay">
        <div class="loading-spinner"></div>
        <div class="loading-text">处理中...</div>
    </div>

    <script src="popup.js"></script>
</body>
</html>
