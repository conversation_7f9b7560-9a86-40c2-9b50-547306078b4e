/* 设置页面样式 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  font-size: 14px;
  line-height: 1.6;
  color: #333;
  background: #f5f5f5;
}

.container {
  max-width: 1000px;
  margin: 0 auto;
  background: white;
  min-height: 100vh;
  box-shadow: 0 0 20px rgba(0,0,0,0.1);
}

/* 头部样式 */
.header {
  background: linear-gradient(135deg, #ff2442, #ff6b6b);
  color: white;
  padding: 20px 30px;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.logo {
  display: flex;
  align-items: center;
  gap: 12px;
}

.icon {
  font-size: 24px;
}

.logo h1 {
  font-size: 20px;
  font-weight: 600;
}

.version {
  background: rgba(255, 255, 255, 0.2);
  padding: 4px 12px;
  border-radius: 16px;
  font-size: 12px;
}

/* 导航标签 */
.nav-tabs {
  display: flex;
  background: #f8f9fa;
  border-bottom: 1px solid #dee2e6;
  padding: 0 30px;
}

.tab-btn {
  background: none;
  border: none;
  padding: 16px 24px;
  cursor: pointer;
  font-size: 14px;
  color: #666;
  border-bottom: 3px solid transparent;
  transition: all 0.2s;
}

.tab-btn:hover {
  color: #333;
  background: rgba(255, 36, 66, 0.05);
}

.tab-btn.active {
  color: #ff2442;
  border-bottom-color: #ff2442;
  background: white;
}

/* 内容区域 */
.content {
  padding: 30px;
  min-height: 600px;
}

.tab-content {
  display: none;
}

.tab-content.active {
  display: block;
}

.section {
  margin-bottom: 40px;
}

.section h2 {
  font-size: 18px;
  margin-bottom: 20px;
  color: #333;
  border-bottom: 2px solid #f0f0f0;
  padding-bottom: 8px;
}

/* 表单样式 */
.form-group {
  margin-bottom: 24px;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: #333;
}

.form-label {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
}

.form-label input[type="checkbox"] {
  margin: 0;
}

input[type="text"],
input[type="number"],
input[type="email"],
select,
textarea {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 14px;
  transition: border-color 0.2s;
}

input[type="text"]:focus,
input[type="number"]:focus,
input[type="email"]:focus,
select:focus,
textarea:focus {
  outline: none;
  border-color: #ff2442;
  box-shadow: 0 0 0 2px rgba(255, 36, 66, 0.1);
}

textarea {
  resize: vertical;
  min-height: 80px;
}

.form-help {
  margin-top: 4px;
  font-size: 12px;
  color: #666;
  line-height: 1.4;
}

/* 按钮样式 */
.btn-primary {
  background: #ff2442;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
}

.btn-primary:hover {
  background: #e01e3c;
}

.btn-secondary {
  background: #f8f9fa;
  color: #333;
  border: 1px solid #ddd;
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 13px;
  cursor: pointer;
  transition: all 0.2s;
}

.btn-secondary:hover {
  background: #e9ecef;
  border-color: #adb5bd;
}

.btn-danger {
  background: #dc3545;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 13px;
  cursor: pointer;
  transition: all 0.2s;
}

.btn-danger:hover {
  background: #c82333;
}

/* 快递规则样式 */
.express-rules {
  border: 1px solid #ddd;
  border-radius: 6px;
  padding: 16px;
  background: #f8f9fa;
  margin-bottom: 12px;
}

.rule-item {
  display: flex;
  gap: 12px;
  align-items: center;
  margin-bottom: 12px;
}

.rule-item:last-child {
  margin-bottom: 0;
}

.region-input {
  flex: 1;
  margin: 0;
}

.express-select {
  flex: 1;
  margin: 0;
}

.remove-rule {
  background: #dc3545;
  color: white;
  border: none;
  padding: 8px 12px;
  border-radius: 4px;
  font-size: 12px;
  cursor: pointer;
  white-space: nowrap;
}

.remove-rule:hover {
  background: #c82333;
}

/* 快递单号池样式 */
.tracking-pool {
  border: 1px solid #ddd;
  border-radius: 6px;
  overflow: hidden;
}

.pool-tabs {
  display: flex;
  background: #f8f9fa;
  border-bottom: 1px solid #ddd;
}

.pool-tab {
  background: none;
  border: none;
  padding: 12px 16px;
  cursor: pointer;
  font-size: 13px;
  color: #666;
  border-bottom: 2px solid transparent;
  transition: all 0.2s;
}

.pool-tab:hover {
  color: #333;
  background: rgba(255, 36, 66, 0.05);
}

.pool-tab.active {
  color: #ff2442;
  border-bottom-color: #ff2442;
  background: white;
}

.pool-content {
  padding: 16px;
}

.pool-content textarea {
  margin-bottom: 12px;
}

.pool-stats {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 13px;
  color: #666;
}

#remaining-count {
  font-weight: 600;
  color: #ff2442;
}

/* 底部操作栏 */
.footer {
  background: #f8f9fa;
  border-top: 1px solid #dee2e6;
  padding: 20px 30px;
  position: sticky;
  bottom: 0;
}

.footer-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.save-status {
  font-size: 13px;
  color: #666;
}

.save-status.success {
  color: #28a745;
}

.save-status.error {
  color: #dc3545;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .container {
    margin: 0;
    box-shadow: none;
  }
  
  .header {
    padding: 16px 20px;
  }
  
  .nav-tabs {
    padding: 0 20px;
    overflow-x: auto;
  }
  
  .tab-btn {
    padding: 12px 16px;
    white-space: nowrap;
  }
  
  .content {
    padding: 20px;
  }
  
  .footer {
    padding: 16px 20px;
  }
  
  .footer-content {
    flex-direction: column;
    gap: 12px;
  }
  
  .rule-item {
    flex-direction: column;
    align-items: stretch;
  }
  
  .pool-tabs {
    overflow-x: auto;
  }
}

/* 动画效果 */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.tab-content.active {
  animation: fadeIn 0.3s ease;
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 复选框样式 */
input[type="checkbox"] {
  width: 16px;
  height: 16px;
  accent-color: #ff2442;
}

/* 数字输入框样式 */
input[type="number"] {
  max-width: 200px;
}

/* 选择框样式 */
select {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
  background-position: right 8px center;
  background-repeat: no-repeat;
  background-size: 16px;
  padding-right: 32px;
  appearance: none;
}

/* 加载状态 */
.loading {
  opacity: 0.6;
  pointer-events: none;
}

.loading::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 20px;
  height: 20px;
  margin: -10px 0 0 -10px;
  border: 2px solid #f3f3f3;
  border-top: 2px solid #ff2442;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
