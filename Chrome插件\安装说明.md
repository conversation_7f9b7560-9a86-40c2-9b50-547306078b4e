# 小红书自动发货助手 - 安装说明

## 🚀 快速安装

### 1. 打开Chrome扩展程序页面
在Chrome浏览器地址栏输入：
```
chrome://extensions/
```
或者通过菜单：**更多工具** → **扩展程序**

### 2. 开启开发者模式
在扩展程序页面右上角，打开 **"开发者模式"** 开关

### 3. 加载插件
1. 点击 **"加载已解压的扩展程序"** 按钮
2. 选择 `Chrome插件` 文件夹
3. 点击 **"选择文件夹"**

### 4. 安装完成
- 插件会立即加载并显示在扩展程序列表中
- 浏览器工具栏会出现插件图标 🚚

## ⚙️ 初始设置

### 1. 配置发货规则
1. 点击插件图标
2. 点击 **"设置"** 按钮
3. 在 **"发货规则"** 标签页配置：
   - 最小/最大发货金额
   - 排除地址（如：新疆、西藏）
   - 排除商品关键词
   - 默认发货备注

### 2. 设置快递公司
在 **"快递管理"** 标签页：
- 选择默认快递公司
- 配置地区快递规则
- 添加快递单号池（可选）

### 3. 通知设置
在 **"通知设置"** 标签页：
- 开启发货成功/失败通知
- 设置每日统计通知

## 📱 使用方法

### 1. 登录小红书商家后台
访问：https://ark.xiaohongshu.com/ 或 https://creator.xiaohongshu.com/

### 2. 进入订单管理页面
导航到订单管理或待发货订单页面

### 3. 开启自动发货
1. 点击插件图标
2. 点击 **"开启自动发货"** 按钮
3. 插件会自动监控并处理待发货订单

### 4. 监控运行状态
- 插件会在页面右上角显示控制面板
- 实时显示运行状态和发货统计
- 可随时暂停或恢复自动发货

## 🔧 功能特性

### ✅ 全自动化
- 后台自动监控订单
- 无需手动操作
- 智能规则匹配
- 批量处理发货

### ✅ 智能规则
- 金额范围限制
- 地址关键词排除
- 商品关键词排除
- 快递公司智能选择

### ✅ 安全可靠
- 操作延迟防检测
- 异常自动重试
- 详细日志记录
- 数据本地存储

### ✅ 用户友好
- 直观的图形界面
- 实时状态显示
- 详细的操作日志
- 灵活的设置选项

## ⚠️ 注意事项

### 使用建议
1. **首次使用建议先测试少量订单**
2. **定期检查发货日志确保正常运行**
3. **保持网络连接稳定**
4. **不要频繁开关自动发货功能**

### 安全提醒
1. **遵守小红书服务条款**
2. **控制操作频率，避免过于频繁**
3. **定期备份重要数据**
4. **不要在多个浏览器同时使用**

### 兼容性
- **支持Chrome 88+版本**
- **支持小红书商家后台最新版本**
- **建议使用最新版本Chrome浏览器**

## 🐛 故障排除

### 插件无法加载
1. 确认已开启开发者模式
2. 检查文件夹路径是否正确
3. 刷新扩展程序页面重试

### 自动发货不工作
1. 确认已登录小红书商家后台
2. 检查是否在订单管理页面
3. 查看插件控制面板状态
4. 检查发货规则设置

### 发货失败
1. 检查网络连接
2. 确认订单状态正确
3. 查看发货日志错误信息
4. 尝试手动发货测试

## 📞 技术支持

如遇到问题：
1. 查看浏览器控制台错误信息
2. 检查插件设置是否正确
3. 尝试重新加载插件
4. 联系技术支持

## 📝 更新日志

### v1.0.0 (2025-07-30)
- 初始版本发布
- 支持自动监控订单
- 支持智能发货规则
- 支持多快递公司
- 支持详细日志记录

---

**祝您使用愉快！** 🎉
