# 小红书自动发货软件开发规则文档

## 项目概述
本项目旨在开发一个小红书自动发货软件，帮助商家自动化处理订单发货流程。

## 项目结构规范

### 文件夹结构
```
小红书自动发货/
├── 永久文件/                    # 永久存储文件夹
│   ├── 配置文件/
│   ├── 用户数据/
│   ├── 日志文件/
│   └── 数据库文件/
├── 临时文件/                    # 临时文件夹（程序结束时自动清理）
│   ├── 缓存文件/
│   ├── 临时下载/
│   └── 处理中文件/
├── 源代码/
├── 文档/
└── 测试文件/
```

### 文件命名规范
1. **所有文件名必须使用中文**
2. 文件名格式：`功能描述.扩展名`
3. 配置文件：`配置_功能名.json`
4. 日志文件：`日志_YYYY-MM-DD.log`
5. 数据文件：`数据_类型名.db`

## 临时文件管理规则

### 自动删除机制
1. **程序启动时**：清理上次运行遗留的临时文件
2. **程序运行中**：定期清理超过1小时的临时文件
3. **程序结束时**：清理所有临时文件
4. **异常退出时**：下次启动时检测并清理

### 临时文件类型
- 缓存文件：`.cache`
- 临时图片：`.tmp.jpg`, `.tmp.png`
- 临时数据：`.tmp.json`
- 处理中文件：`.processing`

## 代码开发规范

### 文件结构
```python
# 主程序文件：主程序.py
# 配置管理：配置管理器.py
# 文件管理：文件管理器.py
# 订单处理：订单处理器.py
# 发货管理：发货管理器.py
# 日志管理：日志管理器.py
```

### 编码规范
1. **字符编码**：统一使用 UTF-8
2. **注释语言**：中文注释
3. **变量命名**：使用有意义的中文拼音或英文
4. **函数命名**：动词+名词形式

### 错误处理
1. 所有文件操作必须包含异常处理
2. 网络请求必须设置超时和重试机制
3. 数据库操作必须包含事务处理
4. 临时文件操作失败不应影响主程序运行

## 功能模块规范

### 核心功能模块
1. **用户认证模块**
   - 小红书账号登录
   - 会话保持
   - 权限验证

2. **订单管理模块**
   - 订单获取
   - 订单状态更新
   - 订单数据存储

3. **发货处理模块**
   - 物流信息录入
   - 发货状态更新
   - 快递单号生成

4. **数据管理模块**
   - 用户数据存储
   - 订单数据管理
   - 统计数据生成

### 安全规范
1. **数据加密**：敏感信息必须加密存储
2. **访问控制**：实现用户权限管理
3. **日志记录**：记录所有关键操作
4. **备份机制**：重要数据定期备份

## 性能优化规范

### 内存管理
1. 及时释放不用的对象
2. 大文件分块处理
3. 限制并发数量
4. 监控内存使用情况

### 网络优化
1. 使用连接池
2. 实现请求缓存
3. 设置合理的超时时间
4. 实现断点续传

## 测试规范

### 测试类型
1. **单元测试**：测试各个功能模块
2. **集成测试**：测试模块间交互
3. **性能测试**：测试系统性能
4. **安全测试**：测试安全漏洞

### 测试文件命名
- 单元测试：`测试_模块名.py`
- 集成测试：`集成测试_功能名.py`
- 性能测试：`性能测试_场景名.py`

## 部署规范

### 环境要求
- Python 3.8+
- 必要的第三方库
- 数据库系统
- 网络环境

### 配置管理
1. 开发环境配置：`开发配置.json`
2. 测试环境配置：`测试配置.json`
3. 生产环境配置：`生产配置.json`

## 维护规范

### 版本管理
1. 版本号格式：`主版本.次版本.修订版本`
2. 更新日志：`更新日志.md`
3. 发布说明：`发布说明_版本号.md`

### 监控告警
1. 系统运行状态监控
2. 错误日志监控
3. 性能指标监控
4. 异常情况告警

---

**注意事项**：
- 严格遵循以上规范进行开发
- 定期review代码质量
- 及时更新文档
- 保持代码简洁易读
