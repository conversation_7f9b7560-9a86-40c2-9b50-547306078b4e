# 小红书自动发货软件 - 数据库设计

## 1. 数据库概览

### 1.1 数据库选择
- **数据库类型**: SQLite
- **文件位置**: `永久文件/数据库文件/数据_主数据库.db`
- **字符编码**: UTF-8
- **版本要求**: SQLite 3.31+

### 1.2 设计原则
- 数据完整性：外键约束、非空约束
- 数据一致性：事务处理、索引优化
- 扩展性：预留字段、版本管理
- 安全性：敏感数据加密存储

## 2. 数据表设计

### 2.1 用户账号表 (用户账号)
```sql
CREATE TABLE 用户账号 (
    账号ID INTEGER PRIMARY KEY AUTOINCREMENT,
    用户名 VARCHAR(50) NOT NULL UNIQUE,
    加密密码 TEXT NOT NULL,
    显示名称 VARCHAR(100),
    手机号 VARCHAR(20),
    邮箱 VARCHAR(100),
    头像URL TEXT,
    账号状态 INTEGER DEFAULT 1, -- 1:正常 0:禁用
    最后登录时间 DATETIME,
    创建时间 DATETIME DEFAULT CURRENT_TIMESTAMP,
    更新时间 DATETIME DEFAULT CURRENT_TIMESTAMP,
    备注 TEXT
);

-- 索引
CREATE INDEX idx_用户账号_用户名 ON 用户账号(用户名);
CREATE INDEX idx_用户账号_状态 ON 用户账号(账号状态);
```

### 2.2 会话管理表 (会话记录)
```sql
CREATE TABLE 会话记录 (
    会话ID VARCHAR(64) PRIMARY KEY,
    账号ID INTEGER NOT NULL,
    访问令牌 TEXT,
    刷新令牌 TEXT,
    过期时间 DATETIME,
    创建时间 DATETIME DEFAULT CURRENT_TIMESTAMP,
    最后活动时间 DATETIME DEFAULT CURRENT_TIMESTAMP,
    客户端信息 TEXT,
    IP地址 VARCHAR(45),
    状态 INTEGER DEFAULT 1, -- 1:活跃 0:过期
    FOREIGN KEY (账号ID) REFERENCES 用户账号(账号ID)
);

-- 索引
CREATE INDEX idx_会话记录_账号ID ON 会话记录(账号ID);
CREATE INDEX idx_会话记录_状态 ON 会话记录(状态);
```

### 2.3 订单信息表 (订单信息)
```sql
CREATE TABLE 订单信息 (
    订单ID VARCHAR(50) PRIMARY KEY,
    账号ID INTEGER NOT NULL,
    订单编号 VARCHAR(100) NOT NULL,
    买家用户名 VARCHAR(100),
    买家昵称 VARCHAR(100),
    收货人姓名 VARCHAR(50),
    收货人电话 VARCHAR(20),
    收货地址 TEXT,
    商品名称 TEXT,
    商品规格 TEXT,
    商品数量 INTEGER,
    订单金额 DECIMAL(10,2),
    支付金额 DECIMAL(10,2),
    订单状态 INTEGER, -- 1:待发货 2:已发货 3:已完成 4:已取消
    支付状态 INTEGER, -- 1:已支付 0:未支付
    下单时间 DATETIME,
    支付时间 DATETIME,
    要求发货时间 DATETIME,
    订单备注 TEXT,
    创建时间 DATETIME DEFAULT CURRENT_TIMESTAMP,
    更新时间 DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (账号ID) REFERENCES 用户账号(账号ID)
);

-- 索引
CREATE INDEX idx_订单信息_账号ID ON 订单信息(账号ID);
CREATE INDEX idx_订单信息_订单状态 ON 订单信息(订单状态);
CREATE INDEX idx_订单信息_下单时间 ON 订单信息(下单时间);
CREATE INDEX idx_订单信息_订单编号 ON 订单信息(订单编号);
```

### 2.4 发货记录表 (发货记录)
```sql
CREATE TABLE 发货记录 (
    发货ID INTEGER PRIMARY KEY AUTOINCREMENT,
    订单ID VARCHAR(50) NOT NULL,
    快递公司 VARCHAR(50),
    快递单号 VARCHAR(50),
    发货时间 DATETIME,
    预计到达时间 DATETIME,
    实际到达时间 DATETIME,
    发货状态 INTEGER, -- 1:已发货 2:运输中 3:已送达 4:异常
    物流信息 TEXT, -- JSON格式存储物流跟踪信息
    发货人员 VARCHAR(50),
    发货备注 TEXT,
    是否自动发货 BOOLEAN DEFAULT 0,
    创建时间 DATETIME DEFAULT CURRENT_TIMESTAMP,
    更新时间 DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (订单ID) REFERENCES 订单信息(订单ID)
);

-- 索引
CREATE INDEX idx_发货记录_订单ID ON 发货记录(订单ID);
CREATE INDEX idx_发货记录_快递单号 ON 发货记录(快递单号);
CREATE INDEX idx_发货记录_发货时间 ON 发货记录(发货时间);
CREATE INDEX idx_发货记录_发货状态 ON 发货记录(发货状态);
```

### 2.5 快递公司表 (快递公司)
```sql
CREATE TABLE 快递公司 (
    公司ID INTEGER PRIMARY KEY AUTOINCREMENT,
    公司名称 VARCHAR(50) NOT NULL UNIQUE,
    公司代码 VARCHAR(20) NOT NULL UNIQUE,
    官网地址 VARCHAR(200),
    客服电话 VARCHAR(20),
    查询接口 VARCHAR(200),
    是否启用 BOOLEAN DEFAULT 1,
    排序权重 INTEGER DEFAULT 0,
    创建时间 DATETIME DEFAULT CURRENT_TIMESTAMP,
    更新时间 DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 索引
CREATE INDEX idx_快递公司_公司代码 ON 快递公司(公司代码);
CREATE INDEX idx_快递公司_是否启用 ON 快递公司(是否启用);

-- 初始数据
INSERT INTO 快递公司 (公司名称, 公司代码, 客服电话, 排序权重) VALUES
('顺丰速运', 'SF', '95338', 100),
('圆通速递', 'YTO', '95554', 90),
('中通快递', 'ZTO', '95311', 85),
('申通快递', 'STO', '95543', 80),
('韵达速递', 'YD', '95546', 75),
('百世快递', 'HTKY', '400-885-6561', 70),
('德邦快递', 'DBL', '95353', 65);
```

### 2.6 系统配置表 (系统配置)
```sql
CREATE TABLE 系统配置 (
    配置ID INTEGER PRIMARY KEY AUTOINCREMENT,
    配置分组 VARCHAR(50) NOT NULL,
    配置键名 VARCHAR(100) NOT NULL,
    配置值 TEXT,
    配置描述 TEXT,
    数据类型 VARCHAR(20) DEFAULT 'string', -- string, int, float, boolean, json
    是否加密 BOOLEAN DEFAULT 0,
    创建时间 DATETIME DEFAULT CURRENT_TIMESTAMP,
    更新时间 DATETIME DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(配置分组, 配置键名)
);

-- 索引
CREATE INDEX idx_系统配置_分组 ON 系统配置(配置分组);
CREATE INDEX idx_系统配置_键名 ON 系统配置(配置键名);

-- 初始配置数据
INSERT INTO 系统配置 (配置分组, 配置键名, 配置值, 配置描述, 数据类型) VALUES
('系统', '自动发货', '0', '是否启用自动发货功能', 'boolean'),
('系统', '发货确认', '1', '发货前是否需要确认', 'boolean'),
('系统', '默认快递公司', 'SF', '默认使用的快递公司代码', 'string'),
('网络', '请求超时', '30', '网络请求超时时间(秒)', 'int'),
('网络', '重试次数', '3', '网络请求失败重试次数', 'int'),
('界面', '主题', '默认', '界面主题设置', 'string'),
('界面', '语言', '中文', '界面语言设置', 'string');
```

### 2.7 操作日志表 (操作日志)
```sql
CREATE TABLE 操作日志 (
    日志ID INTEGER PRIMARY KEY AUTOINCREMENT,
    账号ID INTEGER,
    操作类型 VARCHAR(50) NOT NULL, -- 登录, 发货, 查询, 设置等
    操作对象 VARCHAR(100), -- 订单ID, 配置项等
    操作描述 TEXT,
    操作结果 INTEGER, -- 1:成功 0:失败
    错误信息 TEXT,
    IP地址 VARCHAR(45),
    用户代理 TEXT,
    操作时间 DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (账号ID) REFERENCES 用户账号(账号ID)
);

-- 索引
CREATE INDEX idx_操作日志_账号ID ON 操作日志(账号ID);
CREATE INDEX idx_操作日志_操作类型 ON 操作日志(操作类型);
CREATE INDEX idx_操作日志_操作时间 ON 操作日志(操作时间);
CREATE INDEX idx_操作日志_操作结果 ON 操作日志(操作结果);
```

### 2.8 统计数据表 (统计数据)
```sql
CREATE TABLE 统计数据 (
    统计ID INTEGER PRIMARY KEY AUTOINCREMENT,
    账号ID INTEGER,
    统计日期 DATE NOT NULL,
    订单总数 INTEGER DEFAULT 0,
    发货订单数 INTEGER DEFAULT 0,
    完成订单数 INTEGER DEFAULT 0,
    取消订单数 INTEGER DEFAULT 0,
    订单总金额 DECIMAL(12,2) DEFAULT 0,
    发货总金额 DECIMAL(12,2) DEFAULT 0,
    平均处理时间 INTEGER DEFAULT 0, -- 分钟
    异常订单数 INTEGER DEFAULT 0,
    创建时间 DATETIME DEFAULT CURRENT_TIMESTAMP,
    更新时间 DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (账号ID) REFERENCES 用户账号(账号ID),
    UNIQUE(账号ID, 统计日期)
);

-- 索引
CREATE INDEX idx_统计数据_账号ID ON 统计数据(账号ID);
CREATE INDEX idx_统计数据_统计日期 ON 统计数据(统计日期);
```

## 3. 数据库初始化脚本

### 3.1 创建数据库
```sql
-- 启用外键约束
PRAGMA foreign_keys = ON;

-- 设置日志模式
PRAGMA journal_mode = WAL;

-- 设置同步模式
PRAGMA synchronous = NORMAL;

-- 设置缓存大小
PRAGMA cache_size = 10000;

-- 设置临时存储
PRAGMA temp_store = memory;
```

### 3.2 数据库版本管理
```sql
CREATE TABLE 数据库版本 (
    版本号 VARCHAR(20) PRIMARY KEY,
    描述 TEXT,
    升级脚本 TEXT,
    创建时间 DATETIME DEFAULT CURRENT_TIMESTAMP
);

INSERT INTO 数据库版本 (版本号, 描述) VALUES ('1.0.0', '初始版本');
```

## 4. 数据访问层设计

### 4.1 数据模型类
```python
# 数据模型基类
class 数据模型基类:
    def __init__(self): pass
    def 保存(self): pass
    def 删除(self): pass
    def 更新(self): pass
    def 查找(self): pass

# 具体模型类
class 用户账号模型(数据模型基类): pass
class 订单信息模型(数据模型基类): pass
class 发货记录模型(数据模型基类): pass
```

### 4.2 数据访问对象(DAO)
```python
class 用户账号DAO:
    def 根据用户名查找(self, 用户名): pass
    def 创建用户(self, 用户信息): pass
    def 更新登录时间(self, 账号ID): pass

class 订单信息DAO:
    def 获取待发货订单(self, 账号ID): pass
    def 批量更新订单状态(self, 订单列表): pass
    def 按条件查询订单(self, 查询条件): pass
```

## 5. 数据备份策略

### 5.1 自动备份
- 每日自动备份数据库文件
- 保留最近7天的备份文件
- 备份文件压缩存储

### 5.2 手动备份
- 支持用户手动触发备份
- 支持导出特定数据
- 支持备份文件恢复

---

**文档版本**: 1.0  
**创建日期**: 2025-07-30
