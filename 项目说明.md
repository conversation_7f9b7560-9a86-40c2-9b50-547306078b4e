# 小红书自动发货软件

## 项目简介
这是一个专为小红书商家设计的自动发货软件，能够自动化处理订单发货流程，提高工作效率。

## 项目结构

```
小红书自动发货/
├── 开发规则文档.md              # 开发规范和规则文档
├── 项目说明.md                  # 项目说明文件（本文件）
├── 永久文件/                    # 永久存储文件夹
│   ├── 配置文件/
│   │   └── 项目配置.json        # 主要配置文件
│   ├── 用户数据/                # 用户相关数据
│   ├── 日志文件/                # 系统日志文件
│   └── 数据库文件/              # 数据库文件
├── 临时文件/                    # 临时文件夹（自动清理）
│   ├── 缓存文件/                # 缓存数据
│   ├── 临时下载/                # 临时下载文件
│   └── 处理中文件/              # 正在处理的文件
├── 源代码/                      # 源代码文件夹
│   └── 临时文件管理器.py        # 临时文件自动管理模块
├── 文档/                        # 项目文档
└── 测试文件/                    # 测试相关文件
```

## 核心特性

### 1. 自动临时文件管理
- ✅ 程序启动时自动清理上次遗留的临时文件
- ✅ 运行期间定期清理过期临时文件（默认1小时）
- ✅ 程序退出时自动清理所有临时文件
- ✅ 异常退出后下次启动时检测并清理
- ✅ 支持手动触发清理

### 2. 中文文件名规范
- 所有文件名使用中文命名
- 配置文件：`配置_功能名.json`
- 日志文件：`日志_YYYY-MM-DD.log`
- 数据文件：`数据_类型名.db`

### 3. 智能文件分类
- **永久文件**：配置、用户数据、日志、数据库
- **临时文件**：缓存、临时下载、处理中文件

## 快速开始

### 1. 环境要求
- Python 3.8 或更高版本
- Windows 操作系统

### 2. 运行临时文件管理器
```python
# 导入临时文件管理器
from 源代码.临时文件管理器 import 获取临时文件管理器

# 获取管理器实例
管理器 = 获取临时文件管理器()

# 创建临时文件
临时文件路径 = 管理器.创建临时文件("测试文件.txt", "缓存文件")

# 手动清理
管理器.手动清理()
```

### 3. 配置说明
主要配置文件位于 `永久文件/配置文件/项目配置.json`，包含：
- 项目基本信息
- 文件夹路径配置
- 临时文件管理设置
- 日志配置
- 网络和安全配置

## 开发规范

### 文件命名
- 使用中文文件名
- 源代码文件：`功能名.py`
- 配置文件：`配置_功能名.json`
- 测试文件：`测试_模块名.py`

### 代码规范
- UTF-8 编码
- 中文注释
- 异常处理必须完整
- 临时文件操作要安全

### 临时文件类型
- `.cache` - 缓存文件
- `.tmp.jpg`, `.tmp.png` - 临时图片
- `.tmp.json` - 临时数据
- `.processing` - 处理中文件

## 安全特性

- 🔒 敏感数据加密存储
- 🔒 会话管理和超时控制
- 🔒 访问权限控制
- 🔒 操作日志记录
- 🔒 数据备份机制

## 性能优化

- ⚡ 内存使用监控和限制
- ⚡ 网络连接池
- ⚡ 请求缓存机制
- ⚡ 批量处理优化
- ⚡ 多线程处理

## 日志系统

- 📝 自动按日期分割日志文件
- 📝 多级别日志记录（DEBUG, INFO, WARNING, ERROR）
- 📝 同时输出到文件和控制台
- 📝 日志文件自动清理（保留30天）

## 注意事项

1. **临时文件会被自动删除**，不要在临时文件夹中存储重要数据
2. **所有配置修改**请在 `项目配置.json` 中进行
3. **开发时严格遵循**开发规则文档中的规范
4. **测试功能**前请先备份重要数据

## 技术支持

如有问题请查看：
1. `开发规则文档.md` - 详细的开发规范
2. `永久文件/日志文件/` - 系统运行日志
3. 源代码中的注释说明

---

**版本**: 1.0.0  
**最后更新**: 2025-07-30  
**开发状态**: 开发中
