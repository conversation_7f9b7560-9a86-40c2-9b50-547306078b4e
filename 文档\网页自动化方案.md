# 小红书自动发货 - 网页自动化技术方案

## 1. 技术方案概述

### 1.1 实现原理
由于小红书没有开放商家API，我们采用网页自动化技术：
- 使用Selenium模拟浏览器操作
- 自动登录小红书商家后台
- 解析网页获取订单信息
- 自动填写发货信息
- 处理各种异常情况

### 1.2 技术优势
✅ **无需API权限** - 直接操作网页界面
✅ **功能完整** - 可实现所有手动操作
✅ **实时性好** - 直接获取最新数据
✅ **灵活性高** - 可适应界面变化

### 1.3 技术挑战
⚠️ **反爬虫检测** - 需要模拟真实用户行为
⚠️ **界面变化** - 网页更新可能影响功能
⚠️ **验证码处理** - 需要OCR或人工介入
⚠️ **稳定性** - 网络波动可能导致失败

## 2. 核心技术栈

### 2.1 Selenium WebDriver
```python
# 浏览器自动化
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.service import Service
from webdriver_manager.chrome import ChromeDriverManager
```

### 2.2 验证码识别
```python
# OCR识别
import ddddocr
from PIL import Image
import base64
```

### 2.3 数据解析
```python
# 网页解析
from bs4 import BeautifulSoup
import re
import json
```

## 3. 实现方案详解

### 3.1 浏览器配置
```python
def 创建浏览器驱动():
    """创建Chrome浏览器驱动"""
    options = webdriver.ChromeOptions()
    
    # 基础配置
    options.add_argument('--no-sandbox')
    options.add_argument('--disable-dev-shm-usage')
    options.add_argument('--disable-gpu')
    
    # 反检测配置
    options.add_argument('--disable-blink-features=AutomationControlled')
    options.add_experimental_option("excludeSwitches", ["enable-automation"])
    options.add_experimental_option('useAutomationExtension', False)
    
    # 用户代理
    options.add_argument('--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36')
    
    # 窗口大小
    options.add_argument('--window-size=1920,1080')
    
    # 是否无头模式（可配置）
    if 配置.无头模式:
        options.add_argument('--headless')
    
    # 创建驱动
    service = Service(ChromeDriverManager().install())
    driver = webdriver.Chrome(service=service, options=options)
    
    # 反检测脚本
    driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
    
    return driver
```

### 3.2 登录流程
```python
def 自动登录(driver, 用户名, 密码):
    """自动登录小红书商家后台"""
    try:
        # 1. 访问登录页面
        driver.get("https://ark.xiaohongshu.com/login")
        
        # 2. 等待页面加载
        WebDriverWait(driver, 10).until(
            EC.presence_of_element_located((By.ID, "username"))
        )
        
        # 3. 输入用户名
        username_input = driver.find_element(By.ID, "username")
        username_input.clear()
        模拟人工输入(username_input, 用户名)
        
        # 4. 输入密码
        password_input = driver.find_element(By.ID, "password")
        password_input.clear()
        模拟人工输入(password_input, 密码)
        
        # 5. 处理验证码
        if 检测验证码存在(driver):
            验证码结果 = 处理验证码(driver)
            if not 验证码结果:
                return False, "验证码处理失败"
        
        # 6. 点击登录按钮
        login_button = driver.find_element(By.CLASS_NAME, "login-btn")
        login_button.click()
        
        # 7. 等待登录结果
        WebDriverWait(driver, 10).until(
            lambda d: d.current_url != "https://ark.xiaohongshu.com/login"
        )
        
        # 8. 检查登录状态
        if "dashboard" in driver.current_url:
            return True, "登录成功"
        else:
            return False, "登录失败"
            
    except Exception as e:
        return False, f"登录异常: {str(e)}"

def 模拟人工输入(element, text):
    """模拟人工输入，避免被检测"""
    import time
    import random
    
    for char in text:
        element.send_keys(char)
        time.sleep(random.uniform(0.05, 0.15))  # 随机延迟
```

### 3.3 订单获取
```python
def 获取订单列表(driver):
    """获取待发货订单列表"""
    try:
        # 1. 访问订单管理页面
        driver.get("https://ark.xiaohongshu.com/order/list")
        
        # 2. 等待页面加载
        WebDriverWait(driver, 10).until(
            EC.presence_of_element_located((By.CLASS_NAME, "order-list"))
        )
        
        # 3. 筛选待发货订单
        status_filter = driver.find_element(By.XPATH, "//span[text()='待发货']")
        status_filter.click()
        
        # 4. 等待筛选结果
        time.sleep(2)
        
        # 5. 解析订单数据
        订单列表 = []
        order_elements = driver.find_elements(By.CLASS_NAME, "order-item")
        
        for order_element in order_elements:
            订单数据 = 解析订单元素(order_element)
            if 订单数据:
                订单列表.append(订单数据)
        
        return True, 订单列表
        
    except Exception as e:
        return False, f"获取订单失败: {str(e)}"

def 解析订单元素(order_element):
    """解析单个订单元素"""
    try:
        订单数据 = {}
        
        # 订单编号
        订单数据['订单编号'] = order_element.find_element(
            By.CLASS_NAME, "order-number"
        ).text
        
        # 买家信息
        订单数据['买家昵称'] = order_element.find_element(
            By.CLASS_NAME, "buyer-name"
        ).text
        
        # 商品信息
        订单数据['商品名称'] = order_element.find_element(
            By.CLASS_NAME, "product-name"
        ).text
        
        # 收货地址
        订单数据['收货地址'] = order_element.find_element(
            By.CLASS_NAME, "shipping-address"
        ).text
        
        # 订单金额
        订单数据['订单金额'] = order_element.find_element(
            By.CLASS_NAME, "order-amount"
        ).text
        
        # 下单时间
        订单数据['下单时间'] = order_element.find_element(
            By.CLASS_NAME, "order-time"
        ).text
        
        return 订单数据
        
    except Exception as e:
        print(f"解析订单元素失败: {e}")
        return None
```

### 3.4 自动发货
```python
def 自动发货(driver, 订单ID, 快递公司, 快递单号):
    """自动填写发货信息"""
    try:
        # 1. 找到订单的发货按钮
        发货按钮 = driver.find_element(
            By.XPATH, f"//tr[@data-order-id='{订单ID}']//button[text()='发货']"
        )
        发货按钮.click()
        
        # 2. 等待发货弹窗
        WebDriverWait(driver, 5).until(
            EC.presence_of_element_located((By.CLASS_NAME, "shipping-modal"))
        )
        
        # 3. 选择快递公司
        快递选择器 = driver.find_element(By.CLASS_NAME, "express-select")
        快递选择器.click()
        
        快递选项 = driver.find_element(
            By.XPATH, f"//option[text()='{快递公司}']"
        )
        快递选项.click()
        
        # 4. 输入快递单号
        单号输入框 = driver.find_element(By.CLASS_NAME, "tracking-number")
        单号输入框.clear()
        模拟人工输入(单号输入框, 快递单号)
        
        # 5. 确认发货
        确认按钮 = driver.find_element(By.CLASS_NAME, "confirm-shipping")
        确认按钮.click()
        
        # 6. 等待操作完成
        WebDriverWait(driver, 5).until(
            EC.invisibility_of_element_located((By.CLASS_NAME, "shipping-modal"))
        )
        
        return True, "发货成功"
        
    except Exception as e:
        return False, f"发货失败: {str(e)}"
```

### 3.5 验证码处理
```python
def 处理验证码(driver):
    """处理验证码"""
    try:
        # 1. 查找验证码图片
        captcha_img = driver.find_element(By.CLASS_NAME, "captcha-image")
        
        # 2. 获取验证码图片
        captcha_base64 = captcha_img.screenshot_as_base64
        captcha_bytes = base64.b64decode(captcha_base64)
        
        # 3. OCR识别
        ocr = ddddocr.DdddOcr()
        验证码文本 = ocr.classification(captcha_bytes)
        
        # 4. 输入验证码
        captcha_input = driver.find_element(By.CLASS_NAME, "captcha-input")
        captcha_input.clear()
        captcha_input.send_keys(验证码文本)
        
        return True
        
    except Exception as e:
        print(f"验证码处理失败: {e}")
        # 如果OCR失败，可以弹窗让用户手动输入
        return 手动输入验证码(driver)

def 手动输入验证码(driver):
    """手动输入验证码"""
    try:
        # 保存验证码图片
        captcha_img = driver.find_element(By.CLASS_NAME, "captcha-image")
        captcha_img.screenshot("临时文件/验证码.png")
        
        # 弹窗让用户输入
        import tkinter as tk
        from tkinter import simpledialog
        
        root = tk.Tk()
        root.withdraw()  # 隐藏主窗口
        
        验证码 = simpledialog.askstring(
            "验证码", 
            "请查看临时文件夹中的验证码图片，然后输入验证码："
        )
        
        if 验证码:
            captcha_input = driver.find_element(By.CLASS_NAME, "captcha-input")
            captcha_input.clear()
            captcha_input.send_keys(验证码)
            return True
        
        return False
        
    except Exception as e:
        print(f"手动验证码处理失败: {e}")
        return False
```

## 4. 反检测策略

### 4.1 行为模拟
- 随机延迟操作
- 模拟鼠标移动
- 随机滚动页面
- 模拟人工输入速度

### 4.2 环境伪装
- 修改User-Agent
- 禁用webdriver标识
- 使用真实浏览器配置
- 随机窗口大小

### 4.3 请求控制
- 控制操作频率
- 避免批量操作
- 模拟休息时间
- 错误重试机制

## 5. 异常处理

### 5.1 网络异常
- 超时重试
- 网络检测
- 代理切换
- 降级处理

### 5.2 页面异常
- 元素查找失败
- 页面结构变化
- 弹窗处理
- 页面刷新

### 5.3 业务异常
- 登录失效
- 权限不足
- 订单状态变化
- 发货限制

## 6. 性能优化

### 6.1 资源管理
- 浏览器实例复用
- 内存使用监控
- 临时文件清理
- 连接池管理

### 6.2 并发控制
- 单浏览器实例
- 任务队列管理
- 操作串行化
- 资源锁机制

---

**注意事项**：
1. 此方案仅供学习交流，请遵守小红书服务条款
2. 建议控制操作频率，避免对平台造成压力
3. 定期更新代码以适应页面变化
4. 做好异常处理和日志记录
