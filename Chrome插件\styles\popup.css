/* 弹窗样式 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  width: 350px;
  min-height: 500px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  font-size: 14px;
  line-height: 1.5;
  color: #333;
  background: #f8f9fa;
}

.popup-container {
  padding: 0;
  background: white;
  border-radius: 8px;
  overflow: hidden;
}

/* 头部样式 */
.header {
  background: linear-gradient(135deg, #ff2442, #ff6b6b);
  color: white;
  padding: 16px 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.logo {
  display: flex;
  align-items: center;
  gap: 8px;
}

.icon {
  font-size: 20px;
}

.title {
  font-size: 16px;
  font-weight: 600;
}

.version {
  font-size: 12px;
  opacity: 0.8;
  background: rgba(255, 255, 255, 0.2);
  padding: 2px 8px;
  border-radius: 12px;
}

/* 状态区域 */
.status-section {
  padding: 16px 20px;
  border-bottom: 1px solid #eee;
}

.status-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.status-item:last-child {
  margin-bottom: 0;
}

.status-label {
  font-size: 13px;
  color: #666;
}

.status-value {
  font-size: 13px;
  font-weight: 600;
  padding: 2px 8px;
  border-radius: 4px;
}

.status-value.running {
  color: #52c41a;
  background: #f6ffed;
}

.status-value.stopped {
  color: #ff4d4f;
  background: #fff2f0;
}

.status-value.success {
  color: #52c41a;
  background: #f6ffed;
}

.status-value.warning {
  color: #faad14;
  background: #fffbe6;
}

.status-value.error {
  color: #ff4d4f;
  background: #fff2f0;
}

/* 控制区域 */
.control-section {
  padding: 16px 20px;
  border-bottom: 1px solid #eee;
}

.control-btn {
  width: 100%;
  padding: 12px;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s;
  margin-bottom: 8px;
}

.control-btn:last-child {
  margin-bottom: 0;
}

.control-btn.primary {
  background: #52c41a;
  color: white;
}

.control-btn.primary:hover {
  background: #73d13d;
}

.control-btn.danger {
  background: #ff4d4f;
  color: white;
}

.control-btn.danger:hover {
  background: #ff7875;
}

.control-btn.secondary {
  background: #f0f0f0;
  color: #333;
  border: 1px solid #d9d9d9;
}

.control-btn.secondary:hover {
  background: #e6f7ff;
  border-color: #1890ff;
  color: #1890ff;
}

/* 统计区域 */
.stats-section {
  padding: 16px 20px;
  border-bottom: 1px solid #eee;
}

.stats-title {
  font-size: 14px;
  font-weight: 600;
  margin-bottom: 12px;
  color: #333;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 12px;
}

.stats-item {
  text-align: center;
  padding: 12px 8px;
  background: #f8f9fa;
  border-radius: 6px;
}

.stats-number {
  font-size: 20px;
  font-weight: 700;
  color: #1890ff;
  margin-bottom: 4px;
}

.stats-label {
  font-size: 12px;
  color: #666;
}

/* 快捷操作 */
.quick-actions {
  padding: 16px 20px;
  border-bottom: 1px solid #eee;
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 8px;
}

.action-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 12px 8px;
  background: white;
  border: 1px solid #e8e8e8;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s;
  text-decoration: none;
  color: #333;
}

.action-btn:hover {
  background: #f0f0f0;
  border-color: #1890ff;
}

.action-icon {
  font-size: 18px;
  margin-bottom: 4px;
}

.action-text {
  font-size: 12px;
  text-align: center;
}

/* 最近活动 */
.recent-activity {
  padding: 16px 20px;
  border-bottom: 1px solid #eee;
}

.activity-title {
  font-size: 14px;
  font-weight: 600;
  margin-bottom: 12px;
  color: #333;
}

.activity-list {
  max-height: 120px;
  overflow-y: auto;
}

.activity-item {
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
}

.activity-item:last-child {
  border-bottom: none;
}

.activity-time {
  font-size: 11px;
  color: #999;
  margin-bottom: 2px;
}

.activity-desc {
  font-size: 12px;
  color: #666;
}

/* 底部 */
.footer {
  padding: 12px 20px;
  text-align: center;
  background: #f8f9fa;
}

.footer a {
  color: #1890ff;
  text-decoration: none;
  font-size: 12px;
}

.footer a:hover {
  text-decoration: underline;
}

.separator {
  margin: 0 8px;
  color: #ccc;
}

/* 加载遮罩 */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(255, 255, 255, 0.9);
  display: none;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.loading-spinner {
  width: 32px;
  height: 32px;
  border: 3px solid #f0f0f0;
  border-top: 3px solid #1890ff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 12px;
}

.loading-text {
  font-size: 14px;
  color: #666;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 模态框样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1001;
}

.modal-content {
  background: white;
  border-radius: 8px;
  width: 90%;
  max-width: 400px;
  max-height: 80%;
  overflow: hidden;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid #eee;
}

.modal-header h3 {
  margin: 0;
  font-size: 16px;
}

.modal-close {
  background: none;
  border: none;
  font-size: 20px;
  cursor: pointer;
  color: #999;
}

.modal-close:hover {
  color: #333;
}

.modal-body {
  padding: 20px;
  max-height: 400px;
  overflow-y: auto;
}

.help-content h3,
.about-content h3 {
  margin-bottom: 12px;
  color: #333;
}

.help-content h4,
.about-content h4 {
  margin: 16px 0 8px 0;
  color: #666;
  font-size: 14px;
}

.help-content ol,
.help-content ul,
.about-content ul {
  margin-left: 20px;
  margin-bottom: 12px;
}

.help-content li,
.about-content li {
  margin-bottom: 4px;
  font-size: 13px;
  line-height: 1.4;
}

.copyright {
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid #eee;
  text-align: center;
  font-size: 12px;
  color: #999;
}

/* 错误提示 */
.error-toast {
  position: fixed;
  top: 20px;
  left: 50%;
  transform: translateX(-50%);
  background: #ff4d4f;
  color: white;
  padding: 8px 16px;
  border-radius: 4px;
  font-size: 12px;
  z-index: 1002;
  animation: slideDown 0.3s ease;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateX(-50%) translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(-50%) translateY(0);
  }
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
