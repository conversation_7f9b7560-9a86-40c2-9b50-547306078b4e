<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>小红书自动发货助手 - 设置</title>
    <link rel="stylesheet" href="styles/options.css">
</head>
<body>
    <div class="container">
        <!-- 头部 -->
        <header class="header">
            <div class="header-content">
                <div class="logo">
                    <span class="icon">🚚</span>
                    <h1>小红书自动发货助手</h1>
                </div>
                <div class="version">v1.0.0</div>
            </div>
        </header>

        <!-- 导航标签 -->
        <nav class="nav-tabs">
            <button class="tab-btn active" data-tab="basic">基础设置</button>
            <button class="tab-btn" data-tab="rules">发货规则</button>
            <button class="tab-btn" data-tab="express">快递管理</button>
            <button class="tab-btn" data-tab="notifications">通知设置</button>
            <button class="tab-btn" data-tab="advanced">高级设置</button>
        </nav>

        <!-- 设置内容 -->
        <main class="content">
            <!-- 基础设置 -->
            <div class="tab-content active" id="basic">
                <div class="section">
                    <h2>基础设置</h2>
                    
                    <div class="form-group">
                        <label class="form-label">
                            <input type="checkbox" id="auto-start"> 
                            启动时自动开始监控
                        </label>
                        <p class="form-help">插件加载后自动开始监控订单</p>
                    </div>

                    <div class="form-group">
                        <label for="check-interval">检查间隔 (秒)</label>
                        <input type="number" id="check-interval" min="10" max="300" value="30">
                        <p class="form-help">每隔多少秒检查一次新订单，建议30-60秒</p>
                    </div>

                    <div class="form-group">
                        <label for="max-concurrent">最大并发处理数</label>
                        <input type="number" id="max-concurrent" min="1" max="10" value="3">
                        <p class="form-help">同时处理的订单数量，避免操作过快</p>
                    </div>

                    <div class="form-group">
                        <label for="operation-delay">操作延迟 (毫秒)</label>
                        <input type="number" id="operation-delay" min="500" max="5000" value="2000">
                        <p class="form-help">每个操作之间的延迟时间</p>
                    </div>
                </div>
            </div>

            <!-- 发货规则 -->
            <div class="tab-content" id="rules">
                <div class="section">
                    <h2>发货规则</h2>
                    
                    <div class="form-group">
                        <label for="min-amount">最小发货金额 (元)</label>
                        <input type="number" id="min-amount" min="0" step="0.01" value="0">
                        <p class="form-help">低于此金额的订单不会自动发货</p>
                    </div>

                    <div class="form-group">
                        <label for="max-amount">最大发货金额 (元)</label>
                        <input type="number" id="max-amount" min="0" step="0.01" value="99999">
                        <p class="form-help">高于此金额的订单不会自动发货</p>
                    </div>

                    <div class="form-group">
                        <label for="exclude-addresses">排除地址</label>
                        <textarea id="exclude-addresses" rows="3" placeholder="每行一个地址关键词，如：新疆、西藏、港澳台"></textarea>
                        <p class="form-help">包含这些关键词的地址不会自动发货</p>
                    </div>

                    <div class="form-group">
                        <label for="exclude-products">排除商品</label>
                        <textarea id="exclude-products" rows="3" placeholder="每行一个商品关键词"></textarea>
                        <p class="form-help">包含这些关键词的商品不会自动发货</p>
                    </div>

                    <div class="form-group">
                        <label for="default-note">默认发货备注</label>
                        <textarea id="default-note" rows="2" placeholder="感谢购买，祝您生活愉快！"></textarea>
                        <p class="form-help">自动发货时使用的默认备注</p>
                    </div>
                </div>
            </div>

            <!-- 快递管理 -->
            <div class="tab-content" id="express">
                <div class="section">
                    <h2>快递公司设置</h2>
                    
                    <div class="form-group">
                        <label for="default-express">默认快递公司</label>
                        <select id="default-express">
                            <option value="顺丰速运">顺丰速运</option>
                            <option value="圆通速递">圆通速递</option>
                            <option value="中通快递">中通快递</option>
                            <option value="申通快递">申通快递</option>
                            <option value="韵达速递">韵达速递</option>
                            <option value="百世快递">百世快递</option>
                            <option value="德邦快递">德邦快递</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label>地区快递规则</label>
                        <div class="express-rules" id="express-rules">
                            <div class="rule-item">
                                <input type="text" placeholder="地区关键词" class="region-input">
                                <select class="express-select">
                                    <option value="顺丰速运">顺丰速运</option>
                                    <option value="圆通速递">圆通速递</option>
                                    <option value="中通快递">中通快递</option>
                                    <option value="申通快递">申通快递</option>
                                    <option value="韵达速递">韵达速递</option>
                                </select>
                                <button type="button" class="remove-rule">删除</button>
                            </div>
                        </div>
                        <button type="button" id="add-express-rule" class="btn-secondary">添加规则</button>
                        <p class="form-help">根据收货地址自动选择快递公司</p>
                    </div>

                    <div class="form-group">
                        <label>快递单号池</label>
                        <div class="tracking-pool">
                            <div class="pool-tabs">
                                <button class="pool-tab active" data-express="顺丰速运">顺丰速运</button>
                                <button class="pool-tab" data-express="圆通速递">圆通速递</button>
                                <button class="pool-tab" data-express="中通快递">中通快递</button>
                                <button class="pool-tab" data-express="申通快递">申通快递</button>
                                <button class="pool-tab" data-express="韵达速递">韵达速递</button>
                            </div>
                            <div class="pool-content">
                                <textarea id="tracking-numbers" rows="8" placeholder="每行一个快递单号"></textarea>
                                <div class="pool-stats">
                                    <span>剩余单号: <span id="remaining-count">0</span></span>
                                    <button type="button" id="import-tracking" class="btn-secondary">导入单号</button>
                                </div>
                            </div>
                        </div>
                        <p class="form-help">预先准备好的快递单号，用完后需要重新添加</p>
                    </div>
                </div>
            </div>

            <!-- 通知设置 -->
            <div class="tab-content" id="notifications">
                <div class="section">
                    <h2>通知设置</h2>
                    
                    <div class="form-group">
                        <label class="form-label">
                            <input type="checkbox" id="notify-success"> 
                            发货成功通知
                        </label>
                        <p class="form-help">订单发货成功时显示通知</p>
                    </div>

                    <div class="form-group">
                        <label class="form-label">
                            <input type="checkbox" id="notify-error"> 
                            发货失败通知
                        </label>
                        <p class="form-help">订单发货失败时显示通知</p>
                    </div>

                    <div class="form-group">
                        <label class="form-label">
                            <input type="checkbox" id="notify-daily"> 
                            每日统计通知
                        </label>
                        <p class="form-help">每天结束时显示发货统计</p>
                    </div>

                    <div class="form-group">
                        <label class="form-label">
                            <input type="checkbox" id="sound-notification"> 
                            声音提醒
                        </label>
                        <p class="form-help">通知时播放提示音</p>
                    </div>
                </div>
            </div>

            <!-- 高级设置 -->
            <div class="tab-content" id="advanced">
                <div class="section">
                    <h2>高级设置</h2>
                    
                    <div class="form-group">
                        <label class="form-label">
                            <input type="checkbox" id="debug-mode"> 
                            调试模式
                        </label>
                        <p class="form-help">启用详细日志记录，用于问题排查</p>
                    </div>

                    <div class="form-group">
                        <label class="form-label">
                            <input type="checkbox" id="auto-retry"> 
                            自动重试
                        </label>
                        <p class="form-help">发货失败时自动重试</p>
                    </div>

                    <div class="form-group">
                        <label for="retry-count">重试次数</label>
                        <input type="number" id="retry-count" min="1" max="5" value="3">
                        <p class="form-help">发货失败时的重试次数</p>
                    </div>

                    <div class="form-group">
                        <label for="retry-delay">重试延迟 (秒)</label>
                        <input type="number" id="retry-delay" min="5" max="60" value="10">
                        <p class="form-help">重试之间的等待时间</p>
                    </div>

                    <div class="form-group">
                        <label for="log-retention">日志保留天数</label>
                        <input type="number" id="log-retention" min="1" max="90" value="30">
                        <p class="form-help">自动删除多少天前的日志</p>
                    </div>
                </div>

                <div class="section">
                    <h2>数据管理</h2>
                    
                    <div class="form-group">
                        <button type="button" id="export-settings" class="btn-secondary">导出设置</button>
                        <button type="button" id="import-settings" class="btn-secondary">导入设置</button>
                        <input type="file" id="import-file" accept=".json" style="display: none;">
                        <p class="form-help">备份和恢复插件设置</p>
                    </div>

                    <div class="form-group">
                        <button type="button" id="clear-logs" class="btn-danger">清空日志</button>
                        <button type="button" id="reset-settings" class="btn-danger">重置设置</button>
                        <p class="form-help">清除数据，谨慎操作</p>
                    </div>
                </div>
            </div>
        </main>

        <!-- 底部操作栏 -->
        <footer class="footer">
            <div class="footer-content">
                <button type="button" id="save-settings" class="btn-primary">保存设置</button>
                <button type="button" id="reset-form" class="btn-secondary">重置</button>
                <div class="save-status" id="save-status"></div>
            </div>
        </footer>
    </div>

    <script src="options.js"></script>
</body>
</html>
